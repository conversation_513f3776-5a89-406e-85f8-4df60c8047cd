
services:
  k0s-controller:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: k0s-controller
    hostname: k0s-controller
    privileged: true
    restart: unless-stopped
    init: true
    tmpfs:
      - /tmp
      - /run
    ports:
      - "2222:22"      # SSH access
      - "6443:6443"    # Kubernetes API server
      - "10250:10250"  # Kubelet API
    volumes:
      - /sys/fs/cgroup:/sys/fs/cgroup:rw
      - k0s-controller-data:/var/lib/k0s
      - k0s-controller-config:/etc/k0s
      - k0s-tokens:/var/lib/k0s/tokens
    cap_add:
      - SYS_ADMIN
      - SYS_RESOURCE
    environment:
      - K0S_CONFIG_FILE=/etc/k0s/k0s.yaml
      - K0S_ROLE=controller
    networks:
      - k0s-network
    healthcheck:
      test: ["CMD", "pgrep", "-f", "sshd"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  k0s-worker-1:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: k0s-worker-1
    hostname: k0s-worker-1
    privileged: true
    restart: unless-stopped
    init: true
    tmpfs:
      - /tmp
      - /run
    ports:
      - "2223:22"      # SSH access
    volumes:
      - /sys/fs/cgroup:/sys/fs/cgroup:rw
      - k0s-worker-1-data:/var/lib/k0s
      - k0s-tokens:/var/lib/k0s/tokens:ro
    cap_add:
      - SYS_ADMIN
      - SYS_RESOURCE
    environment:
      - K0S_ROLE=worker
      - K0S_CONTROLLER_HOST=k0s-controller
    networks:
      - k0s-network
    depends_on:
      - k0s-controller
    healthcheck:
      test: ["CMD", "pgrep", "-f", "sshd"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  k0s-worker-2:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: k0s-worker-2
    hostname: k0s-worker-2
    privileged: true
    restart: unless-stopped
    init: true
    tmpfs:
      - /tmp
      - /run
    ports:
      - "2224:22"      # SSH access
    volumes:
      - /sys/fs/cgroup:/sys/fs/cgroup:rw
      - k0s-worker-2-data:/var/lib/k0s
      - k0s-tokens:/var/lib/k0s/tokens:ro
    cap_add:
      - SYS_ADMIN
      - SYS_RESOURCE
    environment:
      - K0S_ROLE=worker
      - K0S_CONTROLLER_HOST=k0s-controller
    networks:
      - k0s-network
    depends_on:
      - k0s-controller
    healthcheck:
      test: ["CMD", "pgrep", "-f", "sshd"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

volumes:
  k0s-controller-data:
    driver: local
  k0s-controller-config:
    driver: local
  k0s-worker-1-data:
    driver: local
  k0s-worker-2-data:
    driver: local
  k0s-tokens:
    driver: local

networks:
  k0s-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
