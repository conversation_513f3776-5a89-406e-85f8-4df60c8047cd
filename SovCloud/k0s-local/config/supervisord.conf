[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid

[program:ssh]
command=/bin/bash -c "mkdir -p /run/sshd && /usr/sbin/sshd -D -e"
stdout_logfile=/var/log/supervisor/ssh.log
stderr_logfile=/var/log/supervisor/ssh.log
autorestart=true
startretries=3

[unix_http_server]
file=/var/run/supervisor.sock
chmod=0700

[supervisorctl]
serverurl=unix:///var/run/supervisor.sock

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface
