# K0s Local Cluster Management

.PHONY: help build start stop restart logs ssh deploy status clean kubeconfig test setup

# Default target
help:
	@echo "K0s Local Cluster Management"
	@echo ""
	@echo "Available targets:"
	@echo "  setup      - Complete setup: build + start + deploy + test"
	@echo "  build      - Build the K0s container image"
	@echo "  start      - Start the K0s cluster container"
	@echo "  stop       - Stop the K0s cluster container"
	@echo "  restart    - Restart the K0s cluster container"
	@echo "  deploy     - Deploy K0s using Ansible"
	@echo "  test       - Run automated cluster tests"
	@echo "  logs       - Show container logs"
	@echo "  ssh        - SSH into the K0s container (requires sshpass)"
	@echo "  status     - Show cluster status"
	@echo "  kubeconfig - Copy kubeconfig to local machine"
	@echo "  clean      - Stop and remove all containers and volumes"
	@echo "  help       - Show this help message"

# Build the container image
build:
	docker-compose build

# Start the cluster
start:
	docker-compose up -d

# Stop the cluster
stop:
	docker-compose down

# Restart the cluster
restart: stop start

# Show container logs
logs:
	docker-compose logs -f k0s-local

# SSH into the container (requires sshpass)
ssh:
	@if command -v sshpass >/dev/null 2>&1; then \
		sshpass -p "k0s" ssh -p 2222 -o StrictHostKeyChecking=no k0s@localhost; \
	else \
		echo "sshpass not found. Install with: brew install sshpass"; \
		echo "Or SSH manually: ssh -p 2222 k0s@localhost (password: k0s)"; \
	fi

# Deploy K0s using Ansible
deploy:
	@echo "Waiting for container to be ready..."
	@sleep 10
	ansible-playbook -i ansible/inventory.ini ansible/playbook.yml

# Run automated tests
test:
	./test-cluster.sh

# Show cluster status
status:
	@echo "=== Container Status ==="
	docker-compose ps
	@echo ""
	@echo "=== K0s Status (if deployed) ==="
	@if command -v sshpass >/dev/null 2>&1; then \
		sshpass -p "k0s" ssh -p 2222 -o StrictHostKeyChecking=no -o ConnectTimeout=5 k0s@localhost "sudo k0s status" 2>/dev/null || echo "K0s not yet deployed or container not ready"; \
	else \
		echo "sshpass not found. Cannot check K0s status automatically."; \
		echo "SSH manually: ssh -p 2222 k0s@localhost 'sudo k0s status'"; \
	fi

# Copy kubeconfig to local machine
kubeconfig:
	@mkdir -p ~/.kube
	@if command -v sshpass >/dev/null 2>&1; then \
		sshpass -p "k0s" scp -P 2222 -o StrictHostKeyChecking=no k0s@localhost:/home/<USER>/kubeconfig ~/.kube/k0s-local-config; \
		echo "Kubeconfig copied to ~/.kube/k0s-local-config"; \
		echo "Use: kubectl --kubeconfig ~/.kube/k0s-local-config get nodes"; \
	else \
		echo "sshpass not found. Install with: brew install sshpass"; \
		echo "Or copy manually: scp -P 2222 k0s@localhost:/home/<USER>/kubeconfig ~/.kube/k0s-local-config"; \
	fi

# Clean up everything
clean:
	docker-compose down -v
	docker rmi k0s-local_k0s-local 2>/dev/null || true

# Full setup (build, start, deploy, test)
setup: build start deploy test
	@echo ""
	@echo "🎉 K0s Cluster Setup Complete! 🎉"
	@echo ""
	@echo "Your cluster is ready! Next steps:"
	@echo "1. SSH into cluster:    make ssh"
	@echo "2. Copy kubeconfig:     make kubeconfig"
	@echo "3. Check status:        make status"
	@echo "4. View logs:           make logs"
	@echo "5. Run tests again:     make test"
	@echo ""
	@echo "Manual access: ssh -p 2222 k0s@localhost (password: k0s)"
