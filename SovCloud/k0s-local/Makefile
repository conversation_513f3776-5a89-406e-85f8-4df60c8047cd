# K0s Local Cluster Management

.PHONY: help build start stop restart logs ssh deploy status clean kubeconfig test setup scale-workers ssh-worker1 ssh-worker2 logs-controller logs-worker1 logs-worker2

# Default target
help:
	@echo "K0s Local Cluster Management"
	@echo ""
	@echo "Available targets:"
	@echo "  setup         - Complete setup: build + start + deploy + test"
	@echo "  build         - Build the K0s container image"
	@echo "  start         - Start the K0s cluster (controller + workers)"
	@echo "  stop          - Stop the K0s cluster"
	@echo "  restart       - Restart the K0s cluster"
	@echo "  deploy        - Deploy K0s using Ansible"
	@echo "  test          - Run automated cluster tests"
	@echo "  logs          - Show all container logs"
	@echo "  ssh           - SSH into the controller (requires sshpass)"
	@echo "  status        - Show cluster status"
	@echo "  kubeconfig    - Copy kubeconfig to local machine"
	@echo "  clean         - Stop and remove all containers and volumes"
	@echo "  scale-workers - Scale worker nodes up/down"
	@echo "  ssh-worker1   - SSH into worker-1 node"
	@echo "  ssh-worker2   - SSH into worker-2 node"
	@echo "  logs-controller - Show controller logs only"
	@echo "  logs-worker1  - Show worker-1 logs only"
	@echo "  logs-worker2  - Show worker-2 logs only"
	@echo "  help          - Show this help message"

# Build the container image
build:
	docker-compose build

# Start the cluster
start:
	docker-compose up -d

# Stop the cluster
stop:
	docker-compose down

# Restart the cluster
restart: stop start

# Show all container logs
logs:
	docker-compose logs -f

# Show specific container logs
logs-controller:
	docker-compose logs -f k0s-controller

logs-worker1:
	docker-compose logs -f k0s-worker-1

logs-worker2:
	docker-compose logs -f k0s-worker-2

# SSH into the controller (requires sshpass)
ssh:
	@if command -v sshpass >/dev/null 2>&1; then \
		sshpass -p "k0s" ssh -p 2222 -o StrictHostKeyChecking=no k0s@localhost; \
	else \
		echo "sshpass not found. Install with: brew install sshpass"; \
		echo "Or SSH manually: ssh -p 2222 k0s@localhost (password: k0s)"; \
	fi

# SSH into worker nodes
ssh-worker1:
	@if command -v sshpass >/dev/null 2>&1; then \
		sshpass -p "k0s" ssh -p 2223 -o StrictHostKeyChecking=no k0s@localhost; \
	else \
		echo "sshpass not found. Install with: brew install sshpass"; \
		echo "Or SSH manually: ssh -p 2223 k0s@localhost (password: k0s)"; \
	fi

ssh-worker2:
	@if command -v sshpass >/dev/null 2>&1; then \
		sshpass -p "k0s" ssh -p 2224 -o StrictHostKeyChecking=no k0s@localhost; \
	else \
		echo "sshpass not found. Install with: brew install sshpass"; \
		echo "Or SSH manually: ssh -p 2224 k0s@localhost (password: k0s)"; \
	fi

# Deploy K0s using Ansible
deploy:
	@echo "Waiting for container to be ready..."
	@sleep 10
	ansible-playbook -i ansible/inventory.ini ansible/playbook.yml

# Run automated tests
test:
	./test-cluster.sh

# Show cluster status
status:
	@echo "=== Container Status ==="
	docker-compose ps
	@echo ""
	@echo "=== K0s Controller Status ==="
	@if command -v sshpass >/dev/null 2>&1; then \
		sshpass -p "k0s" ssh -p 2222 -o StrictHostKeyChecking=no -o ConnectTimeout=5 k0s@localhost "sudo k0s status" 2>/dev/null || echo "Controller not ready"; \
	else \
		echo "sshpass not found. SSH manually: ssh -p 2222 k0s@localhost 'sudo k0s status'"; \
	fi
	@echo ""
	@echo "=== Cluster Nodes ==="
	@if command -v sshpass >/dev/null 2>&1; then \
		sshpass -p "k0s" ssh -p 2222 -o StrictHostKeyChecking=no -o ConnectTimeout=5 k0s@localhost "sudo k0s kubectl get nodes" 2>/dev/null || echo "API not ready"; \
	else \
		echo "sshpass not found. SSH manually: ssh -p 2222 k0s@localhost 'sudo k0s kubectl get nodes'"; \
	fi

# Copy kubeconfig to local machine
kubeconfig:
	@mkdir -p ~/.kube
	@if command -v sshpass >/dev/null 2>&1; then \
		sshpass -p "k0s" scp -P 2222 -o StrictHostKeyChecking=no k0s@localhost:/home/<USER>/kubeconfig ~/.kube/k0s-local-config; \
		echo "Kubeconfig copied to ~/.kube/k0s-local-config"; \
		echo "Use: kubectl --kubeconfig ~/.kube/k0s-local-config get nodes"; \
	else \
		echo "sshpass not found. Install with: brew install sshpass"; \
		echo "Or copy manually: scp -P 2222 k0s@localhost:/home/<USER>/kubeconfig ~/.kube/k0s-local-config"; \
	fi

# Scale worker nodes
scale-workers:
	@echo "Scaling worker nodes..."
	docker-compose up -d --scale k0s-worker-1=1 --scale k0s-worker-2=1
	@echo "Worker nodes scaled. Run 'make test' to verify."

# Clean up everything
clean:
	docker-compose down -v
	docker rmi k0s-local_k0s-controller k0s-local_k0s-worker-1 k0s-local_k0s-worker-2 2>/dev/null || true

# Full setup (build, start, deploy, test)
setup: build start deploy test
	@echo ""
	@echo "🎉 K0s Multi-Node Cluster Setup Complete! 🎉"
	@echo ""
	@echo "Your cluster is ready! Next steps:"
	@echo "1. SSH into controller: make ssh"
	@echo "2. SSH into worker-1:   make ssh-worker1"
	@echo "3. SSH into worker-2:   make ssh-worker2"
	@echo "4. Copy kubeconfig:     make kubeconfig"
	@echo "5. Check status:        make status"
	@echo "6. View logs:           make logs"
	@echo "7. Run tests again:     make test"
	@echo ""
	@echo "Manual access:"
	@echo "  Controller: ssh -p 2222 k0s@localhost (password: k0s)"
	@echo "  Worker-1:   ssh -p 2223 k0s@localhost (password: k0s)"
	@echo "  Worker-2:   ssh -p 2224 k0s@localhost (password: k0s)"
