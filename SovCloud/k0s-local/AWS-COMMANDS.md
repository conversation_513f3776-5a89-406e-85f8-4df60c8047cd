# AWS K0s Cluster Command Reference

## 🚀 **Quick Start Commands**

### **Deploy AWS Infrastructure**
```bash
# Navigate to terraform directory
cd terraform

# Initialize Terraform
terraform init

# Plan deployment (review changes)
terraform plan

# Deploy infrastructure
terraform apply

# Get connection information
terraform output
```

### **Deploy K0s Cluster**
```bash
# Return to project root
cd ..

# Deploy K0s with Ansible
ansible-playbook -i ansible/inventory.ini ansible/playbook.yml

# Test the cluster
./test-aws-cluster.sh
```

## 🔗 **Connection Commands**

### **Get Instance IPs**
```bash
cd terraform
CONTROLLER_IP=$(terraform output -raw controller_public_ip)
WORKER_IPS=$(terraform output -json worker_public_ips)
echo "Controller: $CONTROLLER_IP"
echo "Workers: $WORKER_IPS"
```

### **SSH into Nodes**
```bash
# Controller
ssh -i ~/.ssh/k0s-aws-key ec2-user@$CONTROLLER_IP

# Worker-1
ssh -i ~/.ssh/k0s-aws-key ec2-user@$(echo $WORKER_IPS | jq -r '.[0]')

# Worker-2
ssh -i ~/.ssh/k0s-aws-key ec2-user@$(echo $WORKER_IPS | jq -r '.[1]')

# One-liner for controller
ssh -i ~/.ssh/k0s-aws-key ec2-user@$(cd terraform && terraform output -raw controller_public_ip)
```

## 🎛️ **Controller Operations**

### **Basic Cluster Commands**
```bash
# SSH into controller first
ssh -i ~/.ssh/k0s-aws-key ec2-user@$CONTROLLER_IP

# Once inside controller:
sudo /usr/local/bin/k0s kubectl get nodes -o wide
sudo /usr/local/bin/k0s kubectl get pods --all-namespaces
sudo /usr/local/bin/k0s kubectl cluster-info
sudo /usr/local/bin/k0s status
```

### **Application Deployment**
```bash
# Create deployments
sudo /usr/local/bin/k0s kubectl create deployment nginx --image=nginx --replicas=3
sudo /usr/local/bin/k0s kubectl create deployment webapp --image=httpd --replicas=4

# Expose services
sudo /usr/local/bin/k0s kubectl expose deployment nginx --port=80 --type=ClusterIP
sudo /usr/local/bin/k0s kubectl expose deployment webapp --port=80 --type=NodePort

# Scale deployments
sudo /usr/local/bin/k0s kubectl scale deployment nginx --replicas=5
sudo /usr/local/bin/k0s kubectl scale deployment webapp --replicas=6

# Check pod distribution
sudo /usr/local/bin/k0s kubectl get pods -o wide
```

### **Namespace Management**
```bash
# Create namespaces
sudo /usr/local/bin/k0s kubectl create namespace production
sudo /usr/local/bin/k0s kubectl create namespace staging
sudo /usr/local/bin/k0s kubectl create namespace development

# List namespaces
sudo /usr/local/bin/k0s kubectl get namespaces

# Deploy to specific namespace
sudo /usr/local/bin/k0s kubectl create deployment app --image=nginx --namespace=production
```

### **Monitoring & Debugging**
```bash
# Cluster health
sudo /usr/local/bin/k0s kubectl get componentstatuses
sudo /usr/local/bin/k0s kubectl get events --sort-by=.metadata.creationTimestamp

# Resource usage (if metrics-server is ready)
sudo /usr/local/bin/k0s kubectl top nodes
sudo /usr/local/bin/k0s kubectl top pods --all-namespaces

# System resources
top
free -h
df -h
```

## ⚙️ **Worker Operations**

### **Worker Status**
```bash
# SSH into worker
ssh -i ~/.ssh/k0s-aws-key ec2-user@<worker-ip>

# Check worker status
sudo /usr/local/bin/k0s status

# System monitoring
top
free -h
df -h
sudo netstat -tlnp | grep k0s
```

### **Container Operations**
```bash
# View running containers
sudo docker ps
sudo docker images

# Container logs
sudo docker logs <container-id>

# System logs
sudo tail -f /var/log/k0s-worker.log
sudo journalctl -f
```

## 📋 **Local kubectl Setup**

### **Copy kubeconfig**
```bash
# Copy kubeconfig from controller
scp -i ~/.ssh/k0s-aws-key ec2-user@$CONTROLLER_IP:/home/<USER>/kubeconfig ~/.kube/k0s-aws-config

# Set environment variable
export KUBECONFIG=~/.kube/k0s-aws-config

# Test local access
kubectl get nodes
kubectl get pods --all-namespaces
kubectl cluster-info
```

### **Use kubectl locally**
```bash
# Deploy applications
kubectl create deployment local-nginx --image=nginx --replicas=3
kubectl expose deployment local-nginx --port=80 --type=ClusterIP

# Monitor cluster
kubectl get nodes -o wide
kubectl get pods -o wide
kubectl get services
kubectl get events --sort-by=.metadata.creationTimestamp
```

## 💰 **Cost Management**

### **Check Running Resources**
```bash
# List EC2 instances
aws ec2 describe-instances --query 'Reservations[*].Instances[*].[InstanceId,State.Name,InstanceType,PublicIpAddress]' --output table

# Check costs (requires AWS CLI setup)
aws ce get-cost-and-usage --time-period Start=2024-01-01,End=2024-01-31 --granularity MONTHLY --metrics BlendedCost
```

### **Stop/Destroy Resources**
```bash
# Stop instances (saves costs but keeps data)
cd terraform
aws ec2 stop-instances --instance-ids $(terraform output -json worker_instance_ids | jq -r '.[]')
aws ec2 stop-instances --instance-ids $(terraform output -raw controller_instance_id)

# Destroy entire infrastructure
terraform destroy

# Confirm destruction
terraform show
```

## 🛠️ **Maintenance Commands**

### **Update System**
```bash
# Update all nodes
for ip in $CONTROLLER_IP $(echo $WORKER_IPS | jq -r '.[]'); do
  ssh -i ~/.ssh/k0s-aws-key ec2-user@$ip "sudo yum update -y"
done
```

### **Backup Cluster**
```bash
# Backup from controller
ssh -i ~/.ssh/k0s-aws-key ec2-user@$CONTROLLER_IP "sudo tar -czf k0s-backup-$(date +%Y%m%d).tar.gz /var/lib/k0s"

# Download backup
scp -i ~/.ssh/k0s-aws-key ec2-user@$CONTROLLER_IP:k0s-backup-*.tar.gz ./
```

### **Generate New Tokens**
```bash
# Generate worker token
ssh -i ~/.ssh/k0s-aws-key ec2-user@$CONTROLLER_IP "sudo /usr/local/bin/k0s token create --role=worker"

# Generate admin token
ssh -i ~/.ssh/k0s-aws-key ec2-user@$CONTROLLER_IP "sudo /usr/local/bin/k0s token create --role=admin"
```

## 🔍 **Troubleshooting Commands**

### **Network Debugging**
```bash
# Test connectivity between nodes
ssh -i ~/.ssh/k0s-aws-key ec2-user@$CONTROLLER_IP "ping <worker-private-ip>"

# Check open ports
ssh -i ~/.ssh/k0s-aws-key ec2-user@$CONTROLLER_IP "sudo ss -tlnp | grep 6443"
ssh -i ~/.ssh/k0s-aws-key ec2-user@<worker-ip> "sudo ss -tlnp | grep 10250"

# Monitor network traffic
ssh -i ~/.ssh/k0s-aws-key ec2-user@$CONTROLLER_IP "sudo tcpdump -i any port 6443"
```

### **Log Analysis**
```bash
# K0s logs
ssh -i ~/.ssh/k0s-aws-key ec2-user@$CONTROLLER_IP "sudo tail -f /var/log/k0s-controller.log"
ssh -i ~/.ssh/k0s-aws-key ec2-user@<worker-ip> "sudo tail -f /var/log/k0s-worker.log"

# System logs
ssh -i ~/.ssh/k0s-aws-key ec2-user@$CONTROLLER_IP "sudo journalctl -u k0s -f"
ssh -i ~/.ssh/k0s-aws-key ec2-user@$CONTROLLER_IP "sudo dmesg | tail"
```

### **Process Debugging**
```bash
# Check K0s processes
ssh -i ~/.ssh/k0s-aws-key ec2-user@$CONTROLLER_IP "sudo ps aux | grep k0s"

# Check resource usage
ssh -i ~/.ssh/k0s-aws-key ec2-user@$CONTROLLER_IP "sudo lsof -i :6443"
ssh -i ~/.ssh/k0s-aws-key ec2-user@<worker-ip> "sudo lsof -i :10250"
```

---

**💡 Pro Tips:**
- Always test changes in a development environment first
- Use `terraform plan` before `terraform apply` to review changes
- Keep backups of your cluster data
- Monitor AWS costs regularly
- Use namespaces to organize applications
- Scale deployments based on actual load requirements
