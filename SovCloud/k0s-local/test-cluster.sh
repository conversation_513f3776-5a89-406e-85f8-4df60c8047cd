#!/bin/bash

# K0s Local Cluster Test Script
set -e

echo "🚀 Testing K0s Local Cluster Setup"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

# Test 1: Check if Docker is running
echo "1. Checking Docker..."
if docker info >/dev/null 2>&1; then
    print_status "Docker is running"
else
    print_error "Docker is not running. Please start Docker Desktop."
    exit 1
fi

# Test 2: Check if containers are running
echo "2. Checking container status..."
CONTROLLER_RUNNING=$(docker-compose ps | grep -c "k0s-controller.*Up" || echo "0")
WORKER1_RUNNING=$(docker-compose ps | grep -c "k0s-worker-1.*Up" || echo "0")
WORKER2_RUNNING=$(docker-compose ps | grep -c "k0s-worker-2.*Up" || echo "0")

if [ "$CONTROLLER_RUNNING" -eq 1 ]; then
    print_status "K0s controller is running"
else
    print_warning "K0s controller is not running. Starting cluster..."
    docker-compose up -d
    echo "Waiting for containers to initialize..."
    sleep 20
fi

if [ "$WORKER1_RUNNING" -eq 1 ]; then
    print_status "K0s worker-1 is running"
else
    print_warning "K0s worker-1 is not running"
fi

if [ "$WORKER2_RUNNING" -eq 1 ]; then
    print_status "K0s worker-2 is running"
else
    print_warning "K0s worker-2 is not running"
fi

# Test 3: Check SSH connectivity to all nodes
echo "3. Testing SSH connectivity..."
# Check if sshpass is available
if command -v sshpass >/dev/null 2>&1; then
    # Test controller
    if sshpass -p "k0s" ssh -p 2222 -o StrictHostKeyChecking=no -o ConnectTimeout=10 k0s@localhost "echo 'SSH OK'" 2>/dev/null; then
        print_status "SSH connection to controller successful"
    else
        print_error "SSH connection to controller failed. Container may not be ready yet."
        echo "Try running: docker-compose logs k0s-controller"
        exit 1
    fi

    # Test worker-1
    if sshpass -p "k0s" ssh -p 2223 -o StrictHostKeyChecking=no -o ConnectTimeout=10 k0s@localhost "echo 'SSH OK'" 2>/dev/null; then
        print_status "SSH connection to worker-1 successful"
    else
        print_warning "SSH connection to worker-1 failed. Worker may not be ready yet."
    fi

    # Test worker-2
    if sshpass -p "k0s" ssh -p 2224 -o StrictHostKeyChecking=no -o ConnectTimeout=10 k0s@localhost "echo 'SSH OK'" 2>/dev/null; then
        print_status "SSH connection to worker-2 successful"
    else
        print_warning "SSH connection to worker-2 failed. Worker may not be ready yet."
    fi
else
    print_warning "sshpass not installed. Testing SSH manually..."
    echo "Please test SSH manually:"
    echo "  Controller: ssh -p 2222 k0s@localhost (password: k0s)"
    echo "  Worker-1:   ssh -p 2223 k0s@localhost (password: k0s)"
    echo "  Worker-2:   ssh -p 2224 k0s@localhost (password: k0s)"
    echo "Continuing with other tests..."
fi

# Test 4: Check if K0s is installed
echo "4. Checking K0s installation..."
if command -v sshpass >/dev/null 2>&1; then
    if sshpass -p "k0s" ssh -p 2222 -o StrictHostKeyChecking=no k0s@localhost "which k0s" >/dev/null 2>&1; then
        print_status "K0s binary is installed"
    else
        print_warning "K0s not installed. Running Ansible deployment..."
        ansible-playbook -i ansible/inventory.ini ansible/playbook.yml
    fi
else
    print_warning "Skipping K0s installation check (sshpass not available)"
fi

# Test 5: Check K0s service status
echo "5. Checking K0s service..."
if command -v sshpass >/dev/null 2>&1; then
    if sshpass -p "k0s" ssh -p 2222 -o StrictHostKeyChecking=no k0s@localhost "sudo k0s status" 2>/dev/null | grep -q "Version"; then
        print_status "K0s controller is running"
    else
        print_warning "K0s controller is not active"
    fi
else
    print_warning "Skipping K0s status check (sshpass not available)"
fi

# Test 6: Test Kubernetes API and Worker Nodes
echo "6. Testing Kubernetes API and worker nodes..."
if command -v sshpass >/dev/null 2>&1; then
    if sshpass -p "k0s" ssh -p 2222 -o StrictHostKeyChecking=no k0s@localhost "sudo k0s kubectl get nodes" >/dev/null 2>&1; then
        print_status "Kubernetes API is responding"

        # Show cluster info
        echo ""
        echo "📊 Cluster Information:"
        echo "======================"
        echo "Nodes in cluster:"
        sshpass -p "k0s" ssh -p 2222 -o StrictHostKeyChecking=no k0s@localhost "sudo k0s kubectl get nodes -o wide"

        # Count nodes
        NODE_COUNT=$(sshpass -p "k0s" ssh -p 2222 -o StrictHostKeyChecking=no k0s@localhost "sudo k0s kubectl get nodes --no-headers" 2>/dev/null | wc -l || echo "0")
        echo ""
        echo "Total nodes in cluster: $NODE_COUNT"

        if [ "$NODE_COUNT" -eq 3 ]; then
            print_status "Multi-node cluster detected (Controller + 2 Workers)"
        elif [ "$NODE_COUNT" -eq 2 ]; then
            print_status "Worker-only cluster detected (2 Workers, Controller not schedulable)"
        elif [ "$NODE_COUNT" -eq 1 ]; then
            print_warning "Single-node cluster detected (Controller only)"
            echo "Workers may still be joining..."
        else
            print_warning "Unexpected node count: $NODE_COUNT"
        fi

        echo ""
        echo "Pods across all namespaces:"
        sshpass -p "k0s" ssh -p 2222 -o StrictHostKeyChecking=no k0s@localhost "sudo k0s kubectl get pods --all-namespaces"
    else
        print_error "Kubernetes API is not responding"
        echo "Check logs with: docker-compose logs k0s-controller"
    fi
else
    print_warning "Skipping Kubernetes API test (sshpass not available)"
    echo "To test manually: ssh -p 2222 k0s@localhost 'sudo k0s kubectl get nodes'"
fi

# Test 7: Test worker token generation
echo ""
echo "7. Testing worker token generation..."
if command -v sshpass >/dev/null 2>&1; then
    if sshpass -p "k0s" ssh -p 2222 -o StrictHostKeyChecking=no k0s@localhost "test -f /var/lib/k0s/tokens/worker-token" 2>/dev/null; then
        print_status "Worker token file exists"
        TOKEN_SIZE=$(sshpass -p "k0s" ssh -p 2222 -o StrictHostKeyChecking=no k0s@localhost "wc -c < /var/lib/k0s/tokens/worker-token" 2>/dev/null || echo "0")
        if [ "$TOKEN_SIZE" -gt 100 ]; then
            print_status "Worker token appears valid (size: $TOKEN_SIZE bytes)"
        else
            print_warning "Worker token may be invalid (size: $TOKEN_SIZE bytes)"
        fi
    else
        print_warning "Worker token file not found"
        echo "Token should be generated automatically by the controller"
    fi
else
    print_warning "Skipping worker token test (sshpass not available)"
fi

# Test 8: Test sample deployment
echo ""
echo "8. Testing sample deployment..."
if command -v sshpass >/dev/null 2>&1; then
    sshpass -p "k0s" ssh -p 2222 -o StrictHostKeyChecking=no k0s@localhost "sudo k0s kubectl run test-pod --image=nginx --restart=Never" 2>/dev/null || true
    sleep 5
    if sshpass -p "k0s" ssh -p 2222 -o StrictHostKeyChecking=no k0s@localhost "sudo k0s kubectl get pod test-pod" | grep -q "Running\|Pending"; then
        print_status "Sample pod deployment successful"
        sshpass -p "k0s" ssh -p 2222 -o StrictHostKeyChecking=no k0s@localhost "sudo k0s kubectl delete pod test-pod" >/dev/null 2>&1
    else
        print_warning "Sample pod deployment failed or still starting"
    fi
else
    print_warning "Skipping sample deployment test (sshpass not available)"
fi

echo ""
echo "🎉 K0s Local Cluster Test Complete!"
echo "==================================="
echo ""
echo "Next steps:"
echo "• SSH into controller: ssh -p 2222 k0s@localhost"
echo "• SSH into worker-1:   ssh -p 2223 k0s@localhost"
echo "• SSH into worker-2:   ssh -p 2224 k0s@localhost"
echo "• Copy kubeconfig:     make kubeconfig"
echo "• View logs:           make logs"
echo "• Check status:        make status"
echo "• Scale workers:       make scale-workers"
echo ""
echo "Happy Kubernetes development! 🚢"
