#!/bin/bash

# K0s AWS Cluster Test Script
# Tests the multi-node k0s cluster running on AWS EC2 instances

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

# Get Terraform outputs
get_terraform_outputs() {
    cd terraform
    CONTROLLER_IP=$(terraform output -raw controller_public_ip)
    WORKER_IPS=$(terraform output -json worker_public_ips)
    WORKER1_IP=$(echo $WORKER_IPS | jq -r '.[0]')
    WORKER2_IP=$(echo $WORKER_IPS | jq -r '.[1]')
    cd ..
}

echo "🚀 Testing K0s AWS Multi-Node Cluster"
echo "======================================"

# Test 1: Check Terraform outputs
echo "1. Checking Terraform infrastructure..."
if [ ! -d "terraform" ]; then
    print_error "Terraform directory not found"
    exit 1
fi

get_terraform_outputs

if [ -z "$CONTROLLER_IP" ] || [ -z "$WORKER1_IP" ] || [ -z "$WORKER2_IP" ]; then
    print_error "Failed to get instance IPs from Terraform"
    exit 1
fi

print_status "Infrastructure ready"
print_info "Controller: $CONTROLLER_IP"
print_info "Worker-1:   $WORKER1_IP"
print_info "Worker-2:   $WORKER2_IP"

# Test 2: Check SSH connectivity
echo ""
echo "2. Testing SSH connectivity..."
if command -v ssh >/dev/null 2>&1; then
    # Test controller
    if ssh -o StrictHostKeyChecking=no -o ConnectTimeout=10 -i ~/.ssh/k0s-aws-key ec2-user@$CONTROLLER_IP "echo 'SSH OK'" >/dev/null 2>&1; then
        print_status "SSH connection to controller successful"
    else
        print_error "SSH connection to controller failed"
        exit 1
    fi
    
    # Test worker-1
    if ssh -o StrictHostKeyChecking=no -o ConnectTimeout=10 -i ~/.ssh/k0s-aws-key ec2-user@$WORKER1_IP "echo 'SSH OK'" >/dev/null 2>&1; then
        print_status "SSH connection to worker-1 successful"
    else
        print_warning "SSH connection to worker-1 failed"
    fi
    
    # Test worker-2
    if ssh -o StrictHostKeyChecking=no -o ConnectTimeout=10 -i ~/.ssh/k0s-aws-key ec2-user@$WORKER2_IP "echo 'SSH OK'" >/dev/null 2>&1; then
        print_status "SSH connection to worker-2 successful"
    else
        print_warning "SSH connection to worker-2 failed"
    fi
else
    print_warning "SSH not available for testing"
fi

# Test 3: Check k0s installation
echo ""
echo "3. Checking k0s installation..."
if ssh -o StrictHostKeyChecking=no -i ~/.ssh/k0s-aws-key ec2-user@$CONTROLLER_IP "test -f /usr/local/bin/k0s" 2>/dev/null; then
    print_status "K0s binary is installed on controller"
else
    print_error "K0s binary not found on controller"
    exit 1
fi

# Test 4: Check k0s controller status
echo ""
echo "4. Checking k0s controller status..."
if ssh -o StrictHostKeyChecking=no -i ~/.ssh/k0s-aws-key ec2-user@$CONTROLLER_IP "sudo /usr/local/bin/k0s status" >/dev/null 2>&1; then
    print_status "K0s controller is running"
else
    print_error "K0s controller is not running"
    exit 1
fi

# Test 5: Test Kubernetes API
echo ""
echo "5. Testing Kubernetes API..."
if ssh -o StrictHostKeyChecking=no -i ~/.ssh/k0s-aws-key ec2-user@$CONTROLLER_IP "sudo /usr/local/bin/k0s kubectl get nodes" >/dev/null 2>&1; then
    print_status "Kubernetes API is responding"
    
    # Show cluster info
    echo ""
    echo "📊 Cluster Information:"
    echo "======================"
    echo "Nodes in cluster:"
    ssh -o StrictHostKeyChecking=no -i ~/.ssh/k0s-aws-key ec2-user@$CONTROLLER_IP "sudo /usr/local/bin/k0s kubectl get nodes -o wide"
    
    # Count nodes
    NODE_COUNT=$(ssh -o StrictHostKeyChecking=no -i ~/.ssh/k0s-aws-key ec2-user@$CONTROLLER_IP "sudo /usr/local/bin/k0s kubectl get nodes --no-headers" 2>/dev/null | wc -l || echo "0")
    echo ""
    echo "Total nodes in cluster: $NODE_COUNT"
    
    if [ "$NODE_COUNT" -eq 2 ]; then
        print_status "Multi-node cluster detected (2 Workers)"
    elif [ "$NODE_COUNT" -eq 1 ]; then
        print_warning "Single-node cluster detected"
    else
        print_warning "Unexpected node count: $NODE_COUNT"
    fi
    
else
    print_error "Kubernetes API is not responding"
    exit 1
fi

# Test 6: Test worker token
echo ""
echo "6. Testing worker token..."
if ssh -o StrictHostKeyChecking=no -i ~/.ssh/k0s-aws-key ec2-user@$CONTROLLER_IP "test -f /var/lib/k0s/tokens/worker-token" 2>/dev/null; then
    print_status "Worker token file exists"
    TOKEN_SIZE=$(ssh -o StrictHostKeyChecking=no -i ~/.ssh/k0s-aws-key ec2-user@$CONTROLLER_IP "wc -c < /var/lib/k0s/tokens/worker-token" 2>/dev/null || echo "0")
    if [ "$TOKEN_SIZE" -gt 100 ]; then
        print_status "Worker token appears valid (size: $TOKEN_SIZE bytes)"
    else
        print_warning "Worker token may be invalid (size: $TOKEN_SIZE bytes)"
    fi
else
    print_warning "Worker token file not found"
fi

# Test 7: Test pod deployment
echo ""
echo "7. Testing pod deployment..."
if ssh -o StrictHostKeyChecking=no -i ~/.ssh/k0s-aws-key ec2-user@$CONTROLLER_IP "sudo /usr/local/bin/k0s kubectl run test-aws-nginx --image=nginx --restart=Never" >/dev/null 2>&1; then
    print_status "Test pod created successfully"
    
    # Wait for pod to start
    sleep 10
    
    # Check pod status
    POD_STATUS=$(ssh -o StrictHostKeyChecking=no -i ~/.ssh/k0s-aws-key ec2-user@$CONTROLLER_IP "sudo /usr/local/bin/k0s kubectl get pod test-aws-nginx -o jsonpath='{.status.phase}'" 2>/dev/null || echo "Unknown")
    if [ "$POD_STATUS" = "Running" ]; then
        print_status "Test pod is running"
    else
        print_warning "Test pod status: $POD_STATUS"
    fi
    
    # Show pod details
    echo ""
    echo "Pod deployment details:"
    ssh -o StrictHostKeyChecking=no -i ~/.ssh/k0s-aws-key ec2-user@$CONTROLLER_IP "sudo /usr/local/bin/k0s kubectl get pods -o wide"
    
    # Cleanup test pod
    ssh -o StrictHostKeyChecking=no -i ~/.ssh/k0s-aws-key ec2-user@$CONTROLLER_IP "sudo /usr/local/bin/k0s kubectl delete pod test-aws-nginx test-nginx --ignore-not-found=true" >/dev/null 2>&1
    
else
    print_warning "Failed to create test pod"
fi

# Test 8: Test system pods
echo ""
echo "8. Checking system pods..."
SYSTEM_PODS_RUNNING=$(ssh -o StrictHostKeyChecking=no -i ~/.ssh/k0s-aws-key ec2-user@$CONTROLLER_IP "sudo /usr/local/bin/k0s kubectl get pods -n kube-system --no-headers | grep -c Running" 2>/dev/null || echo "0")
print_status "$SYSTEM_PODS_RUNNING system pods running"

echo ""
echo "System pods status:"
ssh -o StrictHostKeyChecking=no -i ~/.ssh/k0s-aws-key ec2-user@$CONTROLLER_IP "sudo /usr/local/bin/k0s kubectl get pods -n kube-system"

echo ""
echo "🎉 K0s AWS Cluster Test Complete!"
echo ""
echo "Next steps:"
echo "• SSH into controller: ssh -i ~/.ssh/k0s-aws-key ec2-user@$CONTROLLER_IP"
echo "• SSH into worker-1:   ssh -i ~/.ssh/k0s-aws-key ec2-user@$WORKER1_IP"
echo "• SSH into worker-2:   ssh -i ~/.ssh/k0s-aws-key ec2-user@$WORKER2_IP"
echo "• Copy kubeconfig:     scp -i ~/.ssh/k0s-aws-key ec2-user@$CONTROLLER_IP:/home/<USER>/kubeconfig ~/.kube/k0s-aws-config"
echo "• Destroy cluster:     cd terraform && terraform destroy"
echo ""
echo "Happy Kubernetes development on AWS! ☁️"
