apiVersion: k0s.k0sproject.io/v1beta1
kind: ClusterConfig
metadata:
  name: {{ cluster_name }}
spec:
  api:
    port: {{ api_port | default(6443) }}
    sans:
      - 127.0.0.1
      - localhost
      - {{ cluster_name }}
      - k0s-local
  controllerManager: {}
  scheduler: {}
  storage:
    type: etcd
  worker:
    profile: "default"
  network:
    kubeProxy:
      disabled: false
    kuberouter:
      autoMTU: true
    podCIDR: {{ pod_cidr | default('**********/16') }}
    serviceCIDR: {{ service_cidr | default('*********/12') }}
  podSecurityPolicy:
    defaultPolicy: 00-k0s-privileged
  workerProfiles:
  - name: default
    values:
      kubelet:
        cgroupsPerQOS: true
        cgroupDriver: systemd
        failSwapOn: false
