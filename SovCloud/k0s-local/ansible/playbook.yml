---
- name: Deploy K0s Controller Node
  hosts: k0s_controllers
  become: yes
  gather_facts: yes
  vars:
    k0s_version: "v1.33.1+k0s.1"
    k0s_config_dir: "/etc/k0s"
    k0s_data_dir: "/var/lib/k0s"
    cluster_name: "k0s-local"

  tasks:
    - name: Wait for system to be ready
      wait_for_connection:
        timeout: 300

    - name: Gather system facts
      setup:

    - name: Update system packages (Amazon Linux 2)
      shell: yum update -y
      become: yes

    - name: Install required packages for Amazon Linux 2
      shell: yum install -y curl wget python3 python3-pip
      become: yes

    - name: Check if k0s is already installed
      stat:
        path: /usr/local/bin/k0s
      register: k0s_binary

    - name: Download and install k0s binary
      shell: |
        curl -sSLf https://get.k0s.sh | sudo sh
      when: not k0s_binary.stat.exists
      become: yes

    - name: Ensure k0s config directory exists
      file:
        path: "/etc/k0s"
        state: directory
        owner: root
        group: root
        mode: '0755'
      become: yes

    - name: Create k0s configuration file
      template:
        src: k0s-config.yaml.j2
        dest: "/etc/k0s/k0s.yaml"
        owner: root
        group: root
        mode: '0644'
      become: yes

    - name: Check if k0s controller is already running
      shell: k0s status
      register: k0s_status_check
      failed_when: false
      changed_when: false
      become: yes

    - name: Kill any stale k0s processes
      shell: pkill -f "k0s controller" || true
      when: k0s_status_check.rc != 0
      failed_when: false
      become: yes

    - name: Start k0s controller in background
      shell: nohup k0s controller --config /etc/k0s/k0s.yaml > /var/log/k0s-controller.log 2>&1 &
      when: k0s_status_check.rc != 0
      async: 10
      poll: 0
      become: yes

    - name: Wait for k0s to start
      pause:
        seconds: 15

    - name: Wait for k0s API server to be ready
      shell: k0s kubectl get nodes
      register: api_result
      until: api_result.rc == 0
      retries: 30
      delay: 10
      failed_when: false

    - name: Generate kubeconfig for admin access
      command: k0s kubeconfig admin
      register: kubeconfig_output
      changed_when: false

    - name: Save kubeconfig to file
      copy:
        content: "{{ kubeconfig_output.stdout }}"
        dest: "/home/<USER>/kubeconfig"
        owner: "ec2-user"
        group: "ec2-user"
        mode: '0600'
      become: yes

    - name: Create kubectl alias for ec2-user
      lineinfile:
        path: "/home/<USER>/.bashrc"
        line: "alias kubectl='k0s kubectl --kubeconfig=/home/<USER>/kubeconfig'"
        create: yes
        owner: "ec2-user"
        group: "ec2-user"
      become: yes

    - name: Set KUBECONFIG environment variable
      lineinfile:
        path: "/home/<USER>/.bashrc"
        line: "export KUBECONFIG=/home/<USER>/kubeconfig"
        create: yes
        owner: "ec2-user"
        group: "ec2-user"
      become: yes

    - name: Ensure token directory exists
      file:
        path: "/var/lib/k0s/tokens"
        state: directory
        owner: root
        group: root
        mode: '0755'

    - name: Generate worker token
      shell: k0s token create --role=worker
      register: worker_token_output
      changed_when: false

    - name: Save worker token to shared volume
      copy:
        content: "{{ worker_token_output.stdout }}"
        dest: "/var/lib/k0s/tokens/worker-token"
        owner: root
        group: root
        mode: '0644'

    - name: Display cluster status
      command: k0s status
      register: cluster_status
      changed_when: false

    - name: Show controller information
      debug:
        msg: |
          K0s controller is ready!
          Cluster status: {{ cluster_status.stdout }}
          Worker token generated and saved to /var/lib/k0s/tokens/worker-token

          To access the cluster:
          1. SSH into the container: ssh -p 2222 k0s@localhost
          2. Use kubectl: k0s kubectl --kubeconfig=/home/<USER>/kubeconfig get nodes

- name: Deploy K0s Worker Nodes
  hosts: k0s_workers
  become: yes
  gather_facts: yes
  vars:
    k0s_version: "v1.33.1+k0s.1"
    k0s_data_dir: "/var/lib/k0s"
    token_file: "/var/lib/k0s/tokens/worker-token"

  tasks:
    - name: Wait for system to be ready
      wait_for_connection:
        timeout: 300

    - name: Gather system facts
      setup:

    - name: Update system packages (Amazon Linux 2)
      shell: yum update -y
      become: yes

    - name: Install required packages for Amazon Linux 2
      shell: yum install -y curl wget python3 python3-pip
      become: yes

    - name: Check if k0s is already installed
      stat:
        path: /usr/local/bin/k0s
      register: k0s_binary

    - name: Download and install k0s binary
      shell: |
        curl -sSLf https://get.k0s.sh | sudo sh
      when: not k0s_binary.stat.exists
      become: yes

    - name: Get worker token from controller
      shell: ssh -o StrictHostKeyChecking=no -i ~/.ssh/k0s-aws-key ec2-user@{{ hostvars[groups['k0s_controllers'][0]]['ansible_host'] }} "sudo cat /var/lib/k0s/tokens/worker-token"
      register: worker_token_content
      delegate_to: localhost
      become: no

    - name: Check if k0s worker is already running
      shell: k0s status
      register: k0s_worker_status_check
      failed_when: false
      changed_when: false

    - name: Kill any stale k0s processes
      shell: pkill -f "k0s worker" || true
      when: k0s_worker_status_check.rc != 0
      failed_when: false

    - name: Start k0s worker
      shell: |
        nohup k0s worker "{{ worker_token_content.stdout | trim }}" > /var/log/k0s-worker.log 2>&1 &
      when: k0s_worker_status_check.rc != 0
      async: 10
      poll: 0
      become: yes

    - name: Wait for worker to start
      pause:
        seconds: 15

    - name: Check worker status
      shell: k0s status
      register: worker_status
      failed_when: false
      changed_when: false

    - name: Show worker information
      debug:
        msg: |
          K0s worker node status: {{ worker_status.stdout if worker_status.rc == 0 else 'Starting...' }}
          Worker logs: /var/log/k0s-worker.log
