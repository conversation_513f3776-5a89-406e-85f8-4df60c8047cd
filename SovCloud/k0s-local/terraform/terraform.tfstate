{"version": 4, "terraform_version": "1.12.2", "serial": 59, "lineage": "29d0193c-d59b-9477-9a5f-afdb92c075c4", "outputs": {"ansible_inventory": {"value": {"controller": {"host": "*************", "user": "ec2-user"}, "workers": [{"host": "************", "name": "k0s-worker-1", "user": "ec2-user"}, {"host": "*************", "name": "k0s-worker-2", "user": "ec2-user"}]}, "type": ["object", {"controller": ["object", {"host": "string", "user": "string"}], "workers": ["tuple", [["object", {"host": "string", "name": "string", "user": "string"}], ["object", {"host": "string", "name": "string", "user": "string"}]]]}]}, "cluster_info": {"value": {"cluster_name": "k0s-aws-cluster", "controller_ip": "*************", "environment": "dev", "k0s_version": "v1.33.1+k0s.1", "vpc_cidr": "10.0.0.0/16", "worker_count": 2, "worker_ips": ["************", "*************"]}, "type": ["object", {"cluster_name": "string", "controller_ip": "string", "environment": "string", "k0s_version": "string", "vpc_cidr": "string", "worker_count": "number", "worker_ips": ["tuple", ["string", "string"]]}]}, "controller_instance_id": {"value": "i-0b9f3f5212a28befb", "type": "string"}, "controller_private_ip": {"value": "**********", "type": "string"}, "controller_public_ip": {"value": "*************", "type": "string"}, "security_group_id": {"value": "sg-0c1cc2e334ad5088c", "type": "string"}, "ssh_connection_commands": {"value": {"controller": "ssh -i ~/.ssh/k0s-aws-key ec2-user@*************", "workers": ["ssh -i ~/.ssh/k0s-aws-key ec2-user@************", "ssh -i ~/.ssh/k0s-aws-key ec2-user@*************"]}, "type": ["object", {"controller": "string", "workers": ["tuple", ["string", "string"]]}]}, "subnet_id": {"value": "subnet-016c7827384006a84", "type": "string"}, "vpc_id": {"value": "vpc-0780e006116fc844d", "type": "string"}, "worker_instance_ids": {"value": ["i-0923c365c56e94de9", "i-0deb805dfc92c746b"], "type": ["tuple", ["string", "string"]]}, "worker_private_ips": {"value": ["**********", "*********"], "type": ["tuple", ["string", "string"]]}, "worker_public_ips": {"value": ["************", "*************"], "type": ["tuple", ["string", "string"]]}}, "resources": [{"mode": "data", "type": "aws_ami", "name": "amazon_linux", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"architecture": "x86_64", "arn": "arn:aws:ec2:us-east-1::image/ami-02b3c03c6fadb6e2c", "block_device_mappings": [{"device_name": "/dev/xvda", "ebs": {"delete_on_termination": "true", "encrypted": "false", "iops": "0", "snapshot_id": "snap-04ce47de64ef52a80", "throughput": "0", "volume_initialization_rate": "0", "volume_size": "8", "volume_type": "gp2"}, "no_device": "", "virtual_name": ""}], "boot_mode": "", "creation_date": "2025-06-11T01:27:34.000Z", "deprecation_time": "2025-09-09T01:28:00.000Z", "description": "Amazon Linux 2 AMI 2.0.20250610.0 x86_64 HVM gp2", "ena_support": true, "executable_users": null, "filter": [{"name": "name", "values": ["amzn2-ami-hvm-*-x86_64-gp2"]}, {"name": "virtualization-type", "values": ["hvm"]}], "hypervisor": "xen", "id": "ami-02b3c03c6fadb6e2c", "image_id": "ami-02b3c03c6fadb6e2c", "image_location": "amazon/amzn2-ami-hvm-2.0.20250610.0-x86_64-gp2", "image_owner_alias": "amazon", "image_type": "machine", "imds_support": "", "include_deprecated": false, "kernel_id": "", "last_launched_time": "", "most_recent": true, "name": "amzn2-ami-hvm-2.0.20250610.0-x86_64-gp2", "name_regex": null, "owner_id": "137112412989", "owners": ["amazon"], "platform": "", "platform_details": "Linux/UNIX", "product_codes": [], "public": true, "ramdisk_id": "", "root_device_name": "/dev/xvda", "root_device_type": "ebs", "root_snapshot_id": "snap-04ce47de64ef52a80", "sriov_net_support": "simple", "state": "available", "state_reason": {"code": "UNSET", "message": "UNSET"}, "tags": {}, "timeouts": null, "tpm_support": "", "uefi_data": null, "usage_operation": "RunInstances", "virtualization_type": "hvm"}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"mode": "data", "type": "aws_availability_zones", "name": "available", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"all_availability_zones": null, "exclude_names": null, "exclude_zone_ids": null, "filter": null, "group_names": ["us-east-1-zg-1"], "id": "us-east-1", "names": ["us-east-1a", "us-east-1b", "us-east-1c", "us-east-1d", "us-east-1e", "us-east-1f"], "state": "available", "timeouts": null, "zone_ids": ["use1-az6", "use1-az1", "use1-az2", "use1-az4", "use1-az3", "use1-az5"]}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"mode": "managed", "type": "aws_instance", "name": "k0s_controller", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"ami": "ami-02b3c03c6fadb6e2c", "arn": "arn:aws:ec2:us-east-1:982099553881:instance/i-0b9f3f5212a28befb", "associate_public_ip_address": true, "availability_zone": "us-east-1a", "capacity_reservation_specification": [{"capacity_reservation_preference": "open", "capacity_reservation_target": []}], "cpu_core_count": 2, "cpu_options": [{"amd_sev_snp": "", "core_count": 2, "threads_per_core": 1}], "cpu_threads_per_core": 1, "credit_specification": [{"cpu_credits": "standard"}], "disable_api_stop": false, "disable_api_termination": false, "ebs_block_device": [], "ebs_optimized": false, "enable_primary_ipv6": null, "enclave_options": [{"enabled": false}], "ephemeral_block_device": [], "get_password_data": false, "hibernation": false, "host_id": "", "host_resource_group_arn": null, "iam_instance_profile": "", "id": "i-0b9f3f5212a28befb", "instance_initiated_shutdown_behavior": "stop", "instance_lifecycle": "", "instance_market_options": [], "instance_state": "running", "instance_type": "t2.medium", "ipv6_address_count": 0, "ipv6_addresses": [], "key_name": "k0s-aws-cluster-key", "launch_template": [], "maintenance_options": [{"auto_recovery": "default"}], "metadata_options": [{"http_endpoint": "enabled", "http_protocol_ipv6": "disabled", "http_put_response_hop_limit": 1, "http_tokens": "optional", "instance_metadata_tags": "disabled"}], "monitoring": false, "network_interface": [], "outpost_arn": "", "password_data": "", "placement_group": "", "placement_partition_number": 0, "primary_network_interface_id": "eni-0a975b7cd43b67dd5", "private_dns": "ip-10-0-1-161.ec2.internal", "private_dns_name_options": [{"enable_resource_name_dns_a_record": false, "enable_resource_name_dns_aaaa_record": false, "hostname_type": "ip-name"}], "private_ip": "**********", "public_dns": "ec2-13-217-51-224.compute-1.amazonaws.com", "public_ip": "*************", "root_block_device": [{"delete_on_termination": true, "device_name": "/dev/xvda", "encrypted": false, "iops": 100, "kms_key_id": "", "tags": {}, "tags_all": {}, "throughput": 0, "volume_id": "vol-016eeb4f1b8050947", "volume_size": 8, "volume_type": "gp2"}], "secondary_private_ips": [], "security_groups": [], "source_dest_check": true, "spot_instance_request_id": "", "subnet_id": "subnet-016c7827384006a84", "tags": {"Environment": "dev", "Name": "k0s-aws-cluster-controller", "Project": "k0s-cluster", "Role": "controller"}, "tags_all": {"Environment": "dev", "Name": "k0s-aws-cluster-controller", "Project": "k0s-cluster", "Role": "controller"}, "tenancy": "default", "timeouts": null, "user_data": "b7472cb45c40a366f2e8eba4b980a4b605080521", "user_data_base64": null, "user_data_replace_on_change": false, "volume_tags": null, "vpc_security_group_ids": ["sg-0c1cc2e334ad5088c"]}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMCwicmVhZCI6OTAwMDAwMDAwMDAwLCJ1cGRhdGUiOjYwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_key_pair.k0s_key", "aws_security_group.k0s_sg", "aws_subnet.k0s_public_subnet", "aws_vpc.k0s_vpc", "data.aws_ami.amazon_linux", "data.aws_availability_zones.available"]}]}, {"mode": "managed", "type": "aws_instance", "name": "k0s_workers", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 1, "attributes": {"ami": "ami-02b3c03c6fadb6e2c", "arn": "arn:aws:ec2:us-east-1:982099553881:instance/i-0923c365c56e94de9", "associate_public_ip_address": true, "availability_zone": "us-east-1a", "capacity_reservation_specification": [{"capacity_reservation_preference": "open", "capacity_reservation_target": []}], "cpu_core_count": 2, "cpu_options": [{"amd_sev_snp": "", "core_count": 2, "threads_per_core": 1}], "cpu_threads_per_core": 1, "credit_specification": [{"cpu_credits": "standard"}], "disable_api_stop": false, "disable_api_termination": false, "ebs_block_device": [], "ebs_optimized": false, "enable_primary_ipv6": null, "enclave_options": [{"enabled": false}], "ephemeral_block_device": [], "get_password_data": false, "hibernation": false, "host_id": "", "host_resource_group_arn": null, "iam_instance_profile": "", "id": "i-0923c365c56e94de9", "instance_initiated_shutdown_behavior": "stop", "instance_lifecycle": "", "instance_market_options": [], "instance_state": "running", "instance_type": "t2.medium", "ipv6_address_count": 0, "ipv6_addresses": [], "key_name": "k0s-aws-cluster-key", "launch_template": [], "maintenance_options": [{"auto_recovery": "default"}], "metadata_options": [{"http_endpoint": "enabled", "http_protocol_ipv6": "disabled", "http_put_response_hop_limit": 1, "http_tokens": "optional", "instance_metadata_tags": "disabled"}], "monitoring": false, "network_interface": [], "outpost_arn": "", "password_data": "", "placement_group": "", "placement_partition_number": 0, "primary_network_interface_id": "eni-02baa6d535ccd45ba", "private_dns": "ip-10-0-1-116.ec2.internal", "private_dns_name_options": [{"enable_resource_name_dns_a_record": false, "enable_resource_name_dns_aaaa_record": false, "hostname_type": "ip-name"}], "private_ip": "**********", "public_dns": "ec2-54-224-73-44.compute-1.amazonaws.com", "public_ip": "************", "root_block_device": [{"delete_on_termination": true, "device_name": "/dev/xvda", "encrypted": false, "iops": 100, "kms_key_id": "", "tags": {}, "tags_all": {}, "throughput": 0, "volume_id": "vol-0af527f92805566ee", "volume_size": 8, "volume_type": "gp2"}], "secondary_private_ips": [], "security_groups": [], "source_dest_check": true, "spot_instance_request_id": "", "subnet_id": "subnet-016c7827384006a84", "tags": {"Environment": "dev", "Name": "k0s-aws-cluster-worker-1", "Project": "k0s-cluster", "Role": "worker"}, "tags_all": {"Environment": "dev", "Name": "k0s-aws-cluster-worker-1", "Project": "k0s-cluster", "Role": "worker"}, "tenancy": "default", "timeouts": null, "user_data": "d007920d5986f99d9026923f8788b2ec0df1dbde", "user_data_base64": null, "user_data_replace_on_change": false, "volume_tags": null, "vpc_security_group_ids": ["sg-0c1cc2e334ad5088c"]}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMCwicmVhZCI6OTAwMDAwMDAwMDAwLCJ1cGRhdGUiOjYwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_instance.k0s_controller", "aws_key_pair.k0s_key", "aws_security_group.k0s_sg", "aws_subnet.k0s_public_subnet", "aws_vpc.k0s_vpc", "data.aws_ami.amazon_linux", "data.aws_availability_zones.available"]}, {"index_key": 1, "schema_version": 1, "attributes": {"ami": "ami-02b3c03c6fadb6e2c", "arn": "arn:aws:ec2:us-east-1:982099553881:instance/i-0deb805dfc92c746b", "associate_public_ip_address": true, "availability_zone": "us-east-1a", "capacity_reservation_specification": [{"capacity_reservation_preference": "open", "capacity_reservation_target": []}], "cpu_core_count": 2, "cpu_options": [{"amd_sev_snp": "", "core_count": 2, "threads_per_core": 1}], "cpu_threads_per_core": 1, "credit_specification": [{"cpu_credits": "standard"}], "disable_api_stop": false, "disable_api_termination": false, "ebs_block_device": [], "ebs_optimized": false, "enable_primary_ipv6": null, "enclave_options": [{"enabled": false}], "ephemeral_block_device": [], "get_password_data": false, "hibernation": false, "host_id": "", "host_resource_group_arn": null, "iam_instance_profile": "", "id": "i-0deb805dfc92c746b", "instance_initiated_shutdown_behavior": "stop", "instance_lifecycle": "", "instance_market_options": [], "instance_state": "running", "instance_type": "t2.medium", "ipv6_address_count": 0, "ipv6_addresses": [], "key_name": "k0s-aws-cluster-key", "launch_template": [], "maintenance_options": [{"auto_recovery": "default"}], "metadata_options": [{"http_endpoint": "enabled", "http_protocol_ipv6": "disabled", "http_put_response_hop_limit": 1, "http_tokens": "optional", "instance_metadata_tags": "disabled"}], "monitoring": false, "network_interface": [], "outpost_arn": "", "password_data": "", "placement_group": "", "placement_partition_number": 0, "primary_network_interface_id": "eni-0146efd79946ea54d", "private_dns": "ip-10-0-1-44.ec2.internal", "private_dns_name_options": [{"enable_resource_name_dns_a_record": false, "enable_resource_name_dns_aaaa_record": false, "hostname_type": "ip-name"}], "private_ip": "*********", "public_dns": "ec2-184-73-113-98.compute-1.amazonaws.com", "public_ip": "*************", "root_block_device": [{"delete_on_termination": true, "device_name": "/dev/xvda", "encrypted": false, "iops": 100, "kms_key_id": "", "tags": {}, "tags_all": {}, "throughput": 0, "volume_id": "vol-07f57c415c4b608bd", "volume_size": 8, "volume_type": "gp2"}], "secondary_private_ips": [], "security_groups": [], "source_dest_check": true, "spot_instance_request_id": "", "subnet_id": "subnet-016c7827384006a84", "tags": {"Environment": "dev", "Name": "k0s-aws-cluster-worker-2", "Project": "k0s-cluster", "Role": "worker"}, "tags_all": {"Environment": "dev", "Name": "k0s-aws-cluster-worker-2", "Project": "k0s-cluster", "Role": "worker"}, "tenancy": "default", "timeouts": null, "user_data": "d007920d5986f99d9026923f8788b2ec0df1dbde", "user_data_base64": null, "user_data_replace_on_change": false, "volume_tags": null, "vpc_security_group_ids": ["sg-0c1cc2e334ad5088c"]}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMCwicmVhZCI6OTAwMDAwMDAwMDAwLCJ1cGRhdGUiOjYwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_instance.k0s_controller", "aws_key_pair.k0s_key", "aws_security_group.k0s_sg", "aws_subnet.k0s_public_subnet", "aws_vpc.k0s_vpc", "data.aws_ami.amazon_linux", "data.aws_availability_zones.available"]}]}, {"mode": "managed", "type": "aws_internet_gateway", "name": "k0s_igw", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:us-east-1:982099553881:internet-gateway/igw-06f47597019f721b2", "id": "igw-06f47597019f721b2", "owner_id": "982099553881", "tags": {"Environment": "dev", "Name": "k0s-aws-cluster-igw", "Project": "k0s-cluster"}, "tags_all": {"Environment": "dev", "Name": "k0s-aws-cluster-igw", "Project": "k0s-cluster"}, "timeouts": null, "vpc_id": "vpc-0780e006116fc844d"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["aws_vpc.k0s_vpc"]}]}, {"mode": "managed", "type": "aws_key_pair", "name": "k0s_key", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:982099553881:key-pair/k0s-aws-cluster-key", "fingerprint": "b7:32:f1:bd:b5:7e:26:86:07:cf:51:bc:a3:c4:00:00", "id": "k0s-aws-cluster-key", "key_name": "k0s-aws-cluster-key", "key_name_prefix": "", "key_pair_id": "key-05f6bc03a2f3bf510", "key_type": "rsa", "public_key": "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCzMrEF3MNSivgm64MgL/492f6BWV6rgfIraACuxNVrv6UOAUiL8NhRnipJ2Qdj/UKjB4TDOYEql6rqpBOV5aqX1BYZC+lU2waMsJVX4fZZV7B9jYi0hNgifwFeRQNtoM99VoREuMYd9deeWWdDTk+MtAV/9R+OQ3UTM/WShhps/UNEdv2X9AuEA68nzNam5GVD+EVOEMXj60uMoKlUqkxJ7g+A6O3y6rX1W4LlnTygH9eo5Ly2UxEGxcRhIcNnfG37NZeXAAsEWDSWaV0/MKJOXoAeG75RHj9Ld2WqyTxTxChMlOt3EUS2AHotuIUhSiObtiLsfmaoFT+jHP+s1r5l k0s-aws-cluster", "tags": {"Environment": "dev", "Name": "k0s-aws-cluster-key", "Project": "k0s-cluster"}, "tags_all": {"Environment": "dev", "Name": "k0s-aws-cluster-key", "Project": "k0s-cluster"}}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ=="}]}, {"mode": "managed", "type": "aws_route_table", "name": "k0s_public_rt", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:us-east-1:982099553881:route-table/rtb-077bf21d0cc5fb017", "id": "rtb-077bf21d0cc5fb017", "owner_id": "982099553881", "propagating_vgws": [], "route": [{"carrier_gateway_id": "", "cidr_block": "0.0.0.0/0", "core_network_arn": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "igw-06f47597019f721b2", "ipv6_cidr_block": "", "local_gateway_id": "", "nat_gateway_id": "", "network_interface_id": "", "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": ""}], "tags": {"Environment": "dev", "Name": "k0s-aws-cluster-public-rt", "Project": "k0s-cluster"}, "tags_all": {"Environment": "dev", "Name": "k0s-aws-cluster-public-rt", "Project": "k0s-cluster"}, "timeouts": null, "vpc_id": "vpc-0780e006116fc844d"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_internet_gateway.k0s_igw", "aws_vpc.k0s_vpc"]}]}, {"mode": "managed", "type": "aws_route_table_association", "name": "k0s_public_rta", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-05b72be8f677b1ae4", "route_table_id": "rtb-077bf21d0cc5fb017", "subnet_id": "subnet-016c7827384006a84", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_internet_gateway.k0s_igw", "aws_route_table.k0s_public_rt", "aws_subnet.k0s_public_subnet", "aws_vpc.k0s_vpc", "data.aws_availability_zones.available"]}]}, {"mode": "managed", "type": "aws_security_group", "name": "k0s_sg", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:982099553881:security-group/sg-0c1cc2e334ad5088c", "description": "Managed by Terraform", "egress": [{"cidr_blocks": ["0.0.0.0/0"], "description": "All outbound traffic", "from_port": 0, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "-1", "security_groups": [], "self": false, "to_port": 0}], "id": "sg-0c1cc2e334ad5088c", "ingress": [{"cidr_blocks": ["0.0.0.0/0"], "description": "Kubernetes API server", "from_port": 6443, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": [], "self": false, "to_port": 6443}, {"cidr_blocks": ["0.0.0.0/0"], "description": "SSH access", "from_port": 22, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": [], "self": false, "to_port": 22}, {"cidr_blocks": ["10.0.0.0/16"], "description": "Konnectivity server", "from_port": 8132, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": [], "self": false, "to_port": 8132}, {"cidr_blocks": ["10.0.0.0/16"], "description": "Kubelet API", "from_port": 10250, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": [], "self": false, "to_port": 10250}, {"cidr_blocks": ["10.0.0.0/16"], "description": "k0s controller-worker communication", "from_port": 9443, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": [], "self": false, "to_port": 9443}, {"cidr_blocks": [], "description": "Internal cluster communication", "from_port": 0, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": [], "self": true, "to_port": 65535}], "name": "k0s-aws-cluster-sg20250617162104727200000001", "name_prefix": "k0s-aws-cluster-sg", "owner_id": "982099553881", "revoke_rules_on_delete": false, "tags": {"Environment": "dev", "Name": "k0s-aws-cluster-sg", "Project": "k0s-cluster"}, "tags_all": {"Environment": "dev", "Name": "k0s-aws-cluster-sg", "Project": "k0s-cluster"}, "timeouts": null, "vpc_id": "vpc-0780e006116fc844d"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6OTAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0=", "dependencies": ["aws_vpc.k0s_vpc"]}]}, {"mode": "managed", "type": "aws_subnet", "name": "k0s_public_subnet", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:982099553881:subnet/subnet-016c7827384006a84", "assign_ipv6_address_on_creation": false, "availability_zone": "us-east-1a", "availability_zone_id": "use1-az6", "cidr_block": "********/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-016c7827384006a84", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": true, "outpost_arn": "", "owner_id": "982099553881", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Environment": "dev", "Name": "k0s-aws-cluster-public-subnet", "Project": "k0s-cluster"}, "tags_all": {"Environment": "dev", "Name": "k0s-aws-cluster-public-subnet", "Project": "k0s-cluster"}, "timeouts": null, "vpc_id": "vpc-0780e006116fc844d"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.k0s_vpc", "data.aws_availability_zones.available"]}]}, {"mode": "managed", "type": "aws_vpc", "name": "k0s_vpc", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:982099553881:vpc/vpc-0780e006116fc844d", "assign_generated_ipv6_cidr_block": false, "cidr_block": "10.0.0.0/16", "default_network_acl_id": "acl-05699ffbbe7080713", "default_route_table_id": "rtb-00187c5588d882089", "default_security_group_id": "sg-01e8da74fc1acaa71", "dhcp_options_id": "dopt-0d9847653cd118b13", "enable_dns_hostnames": true, "enable_dns_support": true, "enable_network_address_usage_metrics": false, "id": "vpc-0780e006116fc844d", "instance_tenancy": "default", "ipv4_ipam_pool_id": null, "ipv4_netmask_length": null, "ipv6_association_id": "", "ipv6_cidr_block": "", "ipv6_cidr_block_network_border_group": "", "ipv6_ipam_pool_id": "", "ipv6_netmask_length": 0, "main_route_table_id": "rtb-00187c5588d882089", "owner_id": "982099553881", "tags": {"Environment": "dev", "Name": "k0s-aws-cluster-vpc", "Project": "k0s-cluster"}, "tags_all": {"Environment": "dev", "Name": "k0s-aws-cluster-vpc", "Project": "k0s-cluster"}}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ=="}]}], "check_results": null}