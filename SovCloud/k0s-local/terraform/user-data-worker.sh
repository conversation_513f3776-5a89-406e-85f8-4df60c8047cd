#!/bin/bash
# User data script for k0s worker node on Amazon Linux 2

set -e

# Update system
yum update -y

# Install required packages and Python 3.8+
yum install -y curl wget python3 python3-pip

# Install Python 3.8 from Amazon Linux Extras
amazon-linux-extras install python3.8 -y

# Set Python 3.8 as default python3
alternatives --install /usr/bin/python3 python3 /usr/bin/python3.8 1
alternatives --set python3 /usr/bin/python3.8

# Install pip for Python 3.8
curl https://bootstrap.pypa.io/get-pip.py | python3.8

# Install k0s binary
curl -sSLf https://get.k0s.sh | sudo sh

# Create k0s user
useradd -m -s /bin/bash k0s
echo "k0s:k0s" | chpasswd
usermod -aG wheel k0s
echo "k0s ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers

# Configure SSH for k0s user
mkdir -p /home/<USER>/.ssh
cp /home/<USER>/.ssh/authorized_keys /home/<USER>/.ssh/authorized_keys
chown -R k0s:k0s /home/<USER>/.ssh
chmod 700 /home/<USER>/.ssh
chmod 600 /home/<USER>/.ssh/authorized_keys

# Create k0s directories
mkdir -p /var/lib/k0s

# Set hostname based on instance metadata
INSTANCE_ID=$(curl -s http://***************/latest/meta-data/instance-id)
hostnamectl set-hostname ${cluster_name}-worker-$${INSTANCE_ID: -4}

# Log completion
echo "$(date): k0s worker node initialization completed" >> /var/log/user-data.log
