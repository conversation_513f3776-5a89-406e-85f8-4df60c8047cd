# Outputs for K0s Cluster Infrastructure

output "vpc_id" {
  description = "ID of the VPC"
  value       = aws_vpc.k0s_vpc.id
}

output "subnet_id" {
  description = "ID of the public subnet"
  value       = aws_subnet.k0s_public_subnet.id
}

output "security_group_id" {
  description = "ID of the security group"
  value       = aws_security_group.k0s_sg.id
}

output "controller_public_ip" {
  description = "Public IP address of the controller node"
  value       = aws_instance.k0s_controller.public_ip
}

output "controller_private_ip" {
  description = "Private IP address of the controller node"
  value       = aws_instance.k0s_controller.private_ip
}

output "controller_instance_id" {
  description = "Instance ID of the controller node"
  value       = aws_instance.k0s_controller.id
}

output "worker_public_ips" {
  description = "Public IP addresses of worker nodes"
  value       = aws_instance.k0s_workers[*].public_ip
}

output "worker_private_ips" {
  description = "Private IP addresses of worker nodes"
  value       = aws_instance.k0s_workers[*].private_ip
}

output "worker_instance_ids" {
  description = "Instance IDs of worker nodes"
  value       = aws_instance.k0s_workers[*].id
}

output "ssh_connection_commands" {
  description = "SSH commands to connect to instances"
  value = {
    controller = "ssh -i ~/.ssh/k0s-aws-key ec2-user@${aws_instance.k0s_controller.public_ip}"
    workers = [
      for i, worker in aws_instance.k0s_workers :
      "ssh -i ~/.ssh/k0s-aws-key ec2-user@${worker.public_ip}"
    ]
  }
}

output "ansible_inventory" {
  description = "Ansible inventory configuration"
  value = {
    controller = {
      host = aws_instance.k0s_controller.public_ip
      user = "ec2-user"
    }
    workers = [
      for i, worker in aws_instance.k0s_workers : {
        host = worker.public_ip
        user = "ec2-user"
        name = "k0s-worker-${i + 1}"
      }
    ]
  }
}

output "cluster_info" {
  description = "Cluster information summary"
  value = {
    cluster_name     = var.cluster_name
    environment      = var.environment
    controller_ip    = aws_instance.k0s_controller.public_ip
    worker_count     = length(aws_instance.k0s_workers)
    worker_ips       = aws_instance.k0s_workers[*].public_ip
    vpc_cidr         = var.vpc_cidr
    k0s_version      = var.k0s_version
  }
}
