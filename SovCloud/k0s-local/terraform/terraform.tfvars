# Terraform variables for k0s AWS cluster

# AWS Configuration
aws_region = "us-east-1"

# Cluster Configuration
cluster_name = "k0s-aws-cluster"
environment  = "dev"

# Network Configuration
vpc_cidr           = "10.0.0.0/16"
public_subnet_cidr = "********/24"

# Instance Configuration
controller_instance_type = "t2.medium"
worker_instance_type     = "t2.medium"
worker_count            = 2

# SSH Configuration
public_key = "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCzMrEF3MNSivgm64MgL/492f6BWV6rgfIraACuxNVrv6UOAUiL8NhRnipJ2Qdj/UKjB4TDOYEql6rqpBOV5aqX1BYZC+lU2waMsJVX4fZZV7B9jYi0hNgifwFeRQNtoM99VoREuMYd9deeWWdDTk+MtAV/9R+OQ3UTM/WShhps/UNEdv2X9AuEA68nzNam5GVD+EVOEMXj60uMoKlUqkxJ7g+A6O3y6rX1W4LlnTygH9eo5Ly2UxEGxcRhIcNnfG37NZeXAAsEWDSWaV0/MKJOXoAeG75RHj9Ld2WqyTxTxChMlOt3EUS2AHotuIUhSiObtiLsfmaoFT+jHP+s1r5l k0s-aws-cluster"

# Security Configuration
allowed_ssh_cidr = ["0.0.0.0/0"]  # Restrict this in production

# K0s Configuration
k0s_version   = "v1.33.1+k0s.1"
pod_cidr      = "**********/16"
service_cidr  = "*********/12"
