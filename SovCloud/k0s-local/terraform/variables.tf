# Variables for K0s Cluster Infrastructure

variable "aws_region" {
  description = "AWS region for the k0s cluster"
  type        = string
  default     = "us-east-1"
}

variable "cluster_name" {
  description = "Name of the k0s cluster"
  type        = string
  default     = "k0s-aws-cluster"
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
  default     = "dev"
}

variable "vpc_cidr" {
  description = "CIDR block for VPC"
  type        = string
  default     = "10.0.0.0/16"
}

variable "public_subnet_cidr" {
  description = "CIDR block for public subnet"
  type        = string
  default     = "********/24"
}

variable "controller_instance_type" {
  description = "EC2 instance type for controller node"
  type        = string
  default     = "t2.medium"
}

variable "worker_instance_type" {
  description = "EC2 instance type for worker nodes"
  type        = string
  default     = "t2.medium"
}

variable "worker_count" {
  description = "Number of worker nodes"
  type        = number
  default     = 2
}

variable "public_key" {
  description = "Public key for SSH access to instances"
  type        = string
  # This should be provided via terraform.tfvars or environment variable
}

variable "allowed_ssh_cidr" {
  description = "CIDR blocks allowed for SSH access"
  type        = list(string)
  default     = ["0.0.0.0/0"]  # Restrict this in production
}

# K0s specific variables
variable "k0s_version" {
  description = "Version of k0s to install"
  type        = string
  default     = "v1.33.1+k0s.1"
}

variable "pod_cidr" {
  description = "CIDR block for Kubernetes pods"
  type        = string
  default     = "**********/16"
}

variable "service_cidr" {
  description = "CIDR block for Kubernetes services"
  type        = string
  default     = "*********/12"
}
