# K0s Multi-Node Cluster on AWS EC2
# Terraform configuration for 1 controller + 2 worker nodes

terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

# Configure the AWS Provider
provider "aws" {
  region = var.aws_region
}

# Data source for latest Amazon Linux 2 AMI
data "aws_ami" "amazon_linux" {
  most_recent = true
  owners      = ["amazon"]

  filter {
    name   = "name"
    values = ["amzn2-ami-hvm-*-x86_64-gp2"]
  }

  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }
}

# Create VPC
resource "aws_vpc" "k0s_vpc" {
  cidr_block           = var.vpc_cidr
  enable_dns_hostnames = true
  enable_dns_support   = true

  tags = {
    Name        = "${var.cluster_name}-vpc"
    Environment = var.environment
    Project     = "k0s-cluster"
  }
}

# Create Internet Gateway
resource "aws_internet_gateway" "k0s_igw" {
  vpc_id = aws_vpc.k0s_vpc.id

  tags = {
    Name        = "${var.cluster_name}-igw"
    Environment = var.environment
    Project     = "k0s-cluster"
  }
}

# Create public subnet
resource "aws_subnet" "k0s_public_subnet" {
  vpc_id                  = aws_vpc.k0s_vpc.id
  cidr_block              = var.public_subnet_cidr
  availability_zone       = data.aws_availability_zones.available.names[0]
  map_public_ip_on_launch = true

  tags = {
    Name        = "${var.cluster_name}-public-subnet"
    Environment = var.environment
    Project     = "k0s-cluster"
  }
}

# Get available AZs
data "aws_availability_zones" "available" {
  state = "available"
}

# Create route table
resource "aws_route_table" "k0s_public_rt" {
  vpc_id = aws_vpc.k0s_vpc.id

  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = aws_internet_gateway.k0s_igw.id
  }

  tags = {
    Name        = "${var.cluster_name}-public-rt"
    Environment = var.environment
    Project     = "k0s-cluster"
  }
}

# Associate route table with subnet
resource "aws_route_table_association" "k0s_public_rta" {
  subnet_id      = aws_subnet.k0s_public_subnet.id
  route_table_id = aws_route_table.k0s_public_rt.id
}

# Security group for k0s cluster
resource "aws_security_group" "k0s_sg" {
  name_prefix = "${var.cluster_name}-sg"
  vpc_id      = aws_vpc.k0s_vpc.id

  # SSH access
  ingress {
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
    description = "SSH access"
  }

  # Kubernetes API server (controller only)
  ingress {
    from_port   = 6443
    to_port     = 6443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
    description = "Kubernetes API server"
  }

  # Kubelet API
  ingress {
    from_port   = 10250
    to_port     = 10250
    protocol    = "tcp"
    cidr_blocks = [var.vpc_cidr]
    description = "Kubelet API"
  }

  # k0s controller-worker communication
  ingress {
    from_port   = 9443
    to_port     = 9443
    protocol    = "tcp"
    cidr_blocks = [var.vpc_cidr]
    description = "k0s controller-worker communication"
  }

  # Konnectivity server
  ingress {
    from_port   = 8132
    to_port     = 8132
    protocol    = "tcp"
    cidr_blocks = [var.vpc_cidr]
    description = "Konnectivity server"
  }

  # Allow all internal cluster communication
  ingress {
    from_port = 0
    to_port   = 65535
    protocol  = "tcp"
    self      = true
    description = "Internal cluster communication"
  }

  # Allow all outbound traffic
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
    description = "All outbound traffic"
  }

  tags = {
    Name        = "${var.cluster_name}-sg"
    Environment = var.environment
    Project     = "k0s-cluster"
  }
}

# Key pair for SSH access
resource "aws_key_pair" "k0s_key" {
  key_name   = "${var.cluster_name}-key"
  public_key = var.public_key

  tags = {
    Name        = "${var.cluster_name}-key"
    Environment = var.environment
    Project     = "k0s-cluster"
  }
}

# Controller instance
resource "aws_instance" "k0s_controller" {
  ami                    = data.aws_ami.amazon_linux.id
  instance_type          = var.controller_instance_type
  key_name               = aws_key_pair.k0s_key.key_name
  vpc_security_group_ids = [aws_security_group.k0s_sg.id]
  subnet_id              = aws_subnet.k0s_public_subnet.id

  user_data = base64encode(templatefile("${path.module}/user-data-controller.sh", {
    cluster_name = var.cluster_name
  }))

  tags = {
    Name        = "${var.cluster_name}-controller"
    Environment = var.environment
    Project     = "k0s-cluster"
    Role        = "controller"
  }
}

# Worker instances
resource "aws_instance" "k0s_workers" {
  count                  = var.worker_count
  ami                    = data.aws_ami.amazon_linux.id
  instance_type          = var.worker_instance_type
  key_name               = aws_key_pair.k0s_key.key_name
  vpc_security_group_ids = [aws_security_group.k0s_sg.id]
  subnet_id              = aws_subnet.k0s_public_subnet.id

  user_data = base64encode(templatefile("${path.module}/user-data-worker.sh", {
    cluster_name      = var.cluster_name
    controller_ip     = aws_instance.k0s_controller.private_ip
  }))

  tags = {
    Name        = "${var.cluster_name}-worker-${count.index + 1}"
    Environment = var.environment
    Project     = "k0s-cluster"
    Role        = "worker"
  }
}
