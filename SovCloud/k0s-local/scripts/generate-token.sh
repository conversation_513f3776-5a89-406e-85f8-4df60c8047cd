#!/bin/bash

# Generate K0s Worker Token Script
set -e

TOKEN_FILE="/var/lib/k0s/tokens/worker-token"
TOKEN_DIR="/var/lib/k0s/tokens"

echo "Generating k0s worker token..."

# Ensure token directory exists
mkdir -p "${TOKEN_DIR}"

# Check if k0s controller is running
if ! k0s status >/dev/null 2>&1; then
    echo "ERROR: k0s controller is not running"
    echo "Start the controller first before generating tokens"
    exit 1
fi

# Generate new worker token
echo "Creating worker token..."
k0s token create --role=worker > "${TOKEN_FILE}.tmp"

# Verify token was created successfully
if [ -s "${TOKEN_FILE}.tmp" ]; then
    mv "${TOKEN_FILE}.tmp" "${TOKEN_FILE}"
    chmod 644 "${TOKEN_FILE}"
    echo "Worker token generated successfully!"
    echo "Token saved to: ${TOKEN_FILE}"
    echo ""
    echo "Token content:"
    echo "=============="
    cat "${TOKEN_FILE}"
    echo ""
    echo "=============="
else
    echo "ERROR: Failed to generate token"
    rm -f "${TOKEN_FILE}.tmp"
    exit 1
fi

echo "Token generation complete!"
