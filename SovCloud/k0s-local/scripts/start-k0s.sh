#!/bin/bash

# K0s Role-based Startup Script
set -e

# Default values
K0S_ROLE=${K0S_ROLE:-controller}
K0S_CONTROLLER_HOST=${K0S_CONTROLLER_HOST:-localhost}
K0S_CONFIG_FILE=${K0S_CONFIG_FILE:-/etc/k0s/k0s.yaml}
TOKEN_FILE="/var/lib/k0s/tokens/worker-token"

echo "Starting k0s in ${K0S_ROLE} mode..."

case "${K0S_ROLE}" in
    "controller")
        echo "Starting k0s controller..."
        
        # Ensure config directory exists
        mkdir -p /etc/k0s /var/lib/k0s/tokens
        
        # Start controller if not already running
        if ! k0s status >/dev/null 2>&1; then
            echo "Starting k0s controller with config: ${K0S_CONFIG_FILE}"
            nohup k0s controller --config "${K0S_CONFIG_FILE}" > /var/log/k0s-controller.log 2>&1 &
            
            # Wait for controller to be ready
            echo "Waiting for controller to be ready..."
            for i in {1..60}; do
                if k0s status >/dev/null 2>&1; then
                    echo "Controller is ready!"
                    break
                fi
                echo "Waiting... ($i/60)"
                sleep 5
            done
            
            # Generate worker token
            echo "Generating worker token..."
            k0s token create --role=worker > "${TOKEN_FILE}"
            chmod 644 "${TOKEN_FILE}"
            echo "Worker token saved to ${TOKEN_FILE}"
        else
            echo "k0s controller is already running"
        fi
        ;;
        
    "worker")
        echo "Starting k0s worker..."
        
        # Wait for controller to be ready and token to be available
        echo "Waiting for controller and token..."
        for i in {1..120}; do
            if [ -f "${TOKEN_FILE}" ] && [ -s "${TOKEN_FILE}" ]; then
                echo "Token found, attempting to join cluster..."
                break
            fi
            echo "Waiting for token... ($i/120)"
            sleep 5
        done
        
        if [ ! -f "${TOKEN_FILE}" ] || [ ! -s "${TOKEN_FILE}" ]; then
            echo "ERROR: Worker token not found or empty after waiting"
            exit 1
        fi
        
        # Read the token
        TOKEN=$(cat "${TOKEN_FILE}")
        if [ -z "${TOKEN}" ]; then
            echo "ERROR: Token is empty"
            exit 1
        fi
        
        # Start worker if not already running
        if ! k0s status >/dev/null 2>&1; then
            echo "Joining cluster as worker..."
            nohup k0s worker "${TOKEN}" > /var/log/k0s-worker.log 2>&1 &
            
            # Wait for worker to start
            echo "Waiting for worker to start..."
            sleep 10
            
            if k0s status >/dev/null 2>&1; then
                echo "Worker started successfully!"
            else
                echo "WARNING: Worker may not have started properly, check logs"
            fi
        else
            echo "k0s worker is already running"
        fi
        ;;
        
    *)
        echo "ERROR: Unknown K0S_ROLE: ${K0S_ROLE}"
        echo "Valid roles: controller, worker"
        exit 1
        ;;
esac

echo "k0s startup complete for role: ${K0S_ROLE}"
