#!/bin/bash

# Join K0s Worker Node Script
set -e

TOKEN_FILE="/var/lib/k0s/tokens/worker-token"

echo "Joining k0s cluster as worker node..."

# Check if token file exists
if [ ! -f "${TOKEN_FILE}" ]; then
    echo "ERROR: Worker token file not found: ${TOKEN_FILE}"
    echo "Generate a token first on the controller node"
    exit 1
fi

# Read the token
TOKEN=$(cat "${TOKEN_FILE}")
if [ -z "${TOKEN}" ]; then
    echo "ERROR: Token file is empty"
    exit 1
fi

echo "Token found, joining cluster..."

# Check if worker is already running
if k0s status >/dev/null 2>&1; then
    echo "WARNING: k0s is already running on this node"
    echo "Current status:"
    k0s status
    exit 0
fi

# Start worker
echo "Starting k0s worker..."
nohup k0s worker "${TOKEN}" > /var/log/k0s-worker.log 2>&1 &

# Wait for worker to start
echo "Waiting for worker to start..."
sleep 10

# Check status
if k0s status >/dev/null 2>&1; then
    echo "Worker joined cluster successfully!"
    echo "Worker status:"
    k0s status
else
    echo "ERROR: Worker failed to start properly"
    echo "Check logs: tail -f /var/log/k0s-worker.log"
    exit 1
fi

echo "Worker join complete!"
