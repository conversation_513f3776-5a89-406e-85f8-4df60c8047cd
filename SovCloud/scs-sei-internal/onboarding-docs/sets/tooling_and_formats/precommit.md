# Basic pre-commit Commands

## Summary:
This document covers basic pre-commit commands.

## Goals:
* Basic Commands

## Target Audience
* Engineers, All, (T1+)

## Terminology used
* `pre-commit run --all-files` - Runs the pre-commit checks on all files in the repository.
* `pre-commit run --files <file>` - Runs the pre-commit checks on a specific file.
* `pre-commit run -c <config_file>` - Runs the pre-commit checks using a specific configuration file.
* `pre-commit run --hook <hook>` - Runs the pre-commit checks on a specific hook.

## Prerequisites
* Pre-commit installed
* Pre-commit configuration file defined

## Quiz

## Instructions
