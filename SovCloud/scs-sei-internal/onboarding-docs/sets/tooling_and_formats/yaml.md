# Introduction to YAML

## Summary:
This video introduces new users to ansible.  It will cover installation, access, and a few basic commands.

## Goals:
* Basic YAML Structure
  * Document Start
  * Document End
* Dictionaries
* Lists
* Literal vs Folded Block Scalar
* Variable Substitution

## Target Audience
* All,  (T1+)

## Terminology used
* TBD

## Prerequisites

## Quiz
* What is the common syntax for starting and ending a `yaml` document?
* Write a list in two different styles.
* Write a list of dictionaries. Minimum of 3 dictionaries, each dictionary should contain key-value pairs for `name` and `age`
* Write a `dictionary` contained in another `dictionary`.  Each `dictionary` should contain at least 2 key value pairs.
* Write a variable substitution.


## Document Reference
* [ansible yaml reference](https://docs.ansible.com/ansible/latest/reference_appendices/YAMLSyntax.html)

## Video Link
* [basics](https://www.youtube.com/watch?v=0fbnyS_lHW4)
* [block scalar](https://www.youtube.com/watch?v=w5RwmYN8PBo)
* [block scalar 2](https://youtu.be/BEki_rsWu4E?t=194)
* [variable substitutions](https://www.youtube.com/watch?v=kdZwz0NhnkY)
