<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="822px" height="402px" viewBox="-0.5 -0.5 822 402" content="&lt;mxfile&gt;&lt;diagram id=&quot;andUtoVgzvEk1gNckd8C&quot; name=&quot;Page-1&quot;&gt;7ZpLk5s4EIB/DcdN8TJmjhnPOJvancpUXHkdZZBBG4G8Qvixv36FESCQbByMJ56quRjUSAJ/3epuNRjOLNl9oGAdP5EQYsM2w53hPBi27fsu/y0E+1IwmUxLQURRWIqsRrBA/0EhNIU0RyHMWh0ZIZihdVsYkDSFAWvJAKVk2+62Irh91zWIoCJYBACr0m8oZLH4WxOzkf8JURRXd7ZMcSUBVWchyGIQkq0kch4NZ0YJYeVZsptBXLCruJTj5keu1g9GYcrOGTApB2wAzsV/m2GSh1w0hyGkgCGS8sanFO/FA7N9RQGGHIpopiTlh/uYJZi3LH4Kd4h95+fmu4lo/ZCuPBSmYFaNfdVIGd1Lg4rmD/laM+zQqsaVj1Y8z1EMQpSRnAaily0sB9AIil5urQJuupAkkN+Fd6EQcxKb9uxAGFFU92s48xOBWo/de8MuY3f02IW7+MN8Z5oVsrNVIaZ7Jog/S9OFrFYZv21XV/Vdz1Kfrahv8bRQFEVJnoYwFLC2MWJwsQYHDFvuFtt6yxglP2tvYtd0N5AyuDvN9yg52xVuRvjZqrltnJZVeaJYclieebmJOwqjj/fPt8jIaTOyTRWS62kg1TQvoeSqlvT8eWxKBR/Eo9d7jKKUyxgphgDRwnDFxkHp9pO0LA3J2gYvIekrJMfGSAkDDCwPs5karEvCGEkksgHnBuk4bH2zDdexVbg6Kx1jKd/1o4042/VZ1GCI5OYKYTwjmNAmmh2lJdI5MdqokyiZoq+nKFHSObzKcodGl117aqEjq8N+lNhTTXpKG3wET4WhVh89Zs8zdSjrJyEbqXWWId+ddhKaEKRzCZebrWWpYNTcSSXUcgKAsvfFjkEewGVzVNxW5ERhpweXSNf/gYztxR4G5IxwEaEsJhFJAf6bFL5YS97oz66OkD7fYK2Ox/ba1MscTYyRdxGnp+kqr8z+lGmGGL/G1l+RSnWL6boK7nokexwF+1dTsPOm4PMU/Go06r5p9Jc02t1WDNRo15ePqNHJm0YvccK3r2B1L/X1eaYo/ffv3b0OEU19Q7cnGmPjbqtp+G0ymnYYTV6QkVoDegIpiGACD3Z9BWAvVepwOmva1mzHHVfDdZRahz1VwP4F6RJSkhntEnK/nx6rdmy1KsdNIflI7bgVAQIMsgwFnSBgDS8xi5xDrjGX7vJUkdlyq0pHXcAyBlQF+J8CcnxYFx45Ox4d3G5lx+u8sunpX7Ub2ymf4KoFcLUkNM9ZTqFRMPNwscqWlJ9FxZlkm+pFyVqd+VCDlcy1Md5xDTYEWXzwS0r+8hLWe+mLqUGWWceKaj34py1T6e+dsMwhVqdWyx8QhQEjBxgLSDeIQzweUqwhIQXQQGSZ7tklsJ7g0c1bPDV4+NeKyWpu9yWDVF2X/AiSAk66zIqDHLmvvkgHLpzqWwDZ7b/UK91qDvmdLkm5OQGUcuNT+D6m4brO1W8rZZz4/fY5vZZ9ql8kcGL/5sUHEfezPGN8StpIyiwyu0GItq8P630Qx3hho/m+4JVCvOtAnF4NIm82n92Uwan5dsl5/B8=&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <g>
        <path d="M 261 61 L 61.72 158.21" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 57 160.51 L 61.76 154.3 L 61.72 158.21 L 64.83 160.59 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 111px; margin-left: 159px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                Cloud Federation Only
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="159" y="114" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    Cloud Federation Only
                </text>
            </switch>
        </g>
        <path d="M 261 61 L 415.6 157.62" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 420.05 160.41 L 412.26 159.67 L 415.6 157.62 L 415.97 153.73 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 111px; margin-left: 341px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                Cloud Federation Only
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="341" y="114" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    Cloud Federation Only
                </text>
            </switch>
        </g>
        <rect x="201" y="1" width="120" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 31px; margin-left: 202px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                SMS
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="261" y="35" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    SMS
                </text>
            </switch>
        </g>
        <rect x="191" y="161" width="460" height="240" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 458px; height: 1px; padding-top: 281px; margin-left: 192px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                IBP
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="421" y="285" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    IBP
                </text>
            </switch>
        </g>
        <rect x="1" y="161" width="110" height="120" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 108px; height: 1px; padding-top: 168px; margin-left: 3px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                SPR
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="3" y="180" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">
                    SPR
                </text>
            </switch>
        </g>
        <rect x="761" y="281" width="60" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/>
        <ellipse cx="791" cy="297" rx="6" ry="6" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="none"/>
        <path d="M 791 303 L 791 317" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 791 309 L 801 303" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 791 309 L 781 303" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 791 317 L 781 331" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 791 317 L 801 331" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/>
        <rect x="21" y="201" width="60" height="40" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 221px; margin-left: 22px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                VPC
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="51" y="225" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    VPC
                </text>
            </switch>
        </g>
        <rect x="31" y="211" width="60" height="40" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 231px; margin-left: 32px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                VPC
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="61" y="235" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    VPC
                </text>
            </switch>
        </g>
        <rect x="281" y="181" width="340" height="120" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 338px; height: 1px; padding-top: 188px; margin-left: 283px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                Management VPC
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="283" y="200" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px">
                    Management VPC
                </text>
            </switch>
        </g>
        <path d="M 361 267.37 L 361 311 Q 361 321 361 331 L 361 351 Q 361 361 351 361 L 327.37 361" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 361 262.12 L 364.5 269.12 L 361 267.37 L 357.5 269.12 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 322.12 361 L 329.12 357.5 L 327.37 361 L 329.12 364.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 321px; margin-left: 381px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                Kerberos Federation
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="381" y="324" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    Kerberos Federation
                </text>
            </switch>
        </g>
        <path d="M 314.63 241 L 221 241 Q 211 241 211 231 L 211 226 Q 211 221 201 221 L 117.37 221" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="none"/>
        <path d="M 319.88 241 L 312.88 244.5 L 314.63 241 L 312.88 237.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 112.12 221 L 119.12 217.5 L 117.37 221 L 119.12 224.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 236px; margin-left: 211px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                Future
                                <br/>
                                Kerberos
                                <br/>
                                Federation?
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="211" y="239" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    Future...
                </text>
            </switch>
        </g>
        <rect x="321" y="221" width="80" height="40" rx="16" ry="16" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 241px; margin-left: 322px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                Directory Service
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="361" y="245" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Directory Ser...
                </text>
            </switch>
        </g>
        <path d="M 541 241 L 407.37 241" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <path d="M 402.12 241 L 409.12 237.5 L 407.37 241 L 409.12 244.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 241px; margin-left: 471px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                User
                                <br/>
                                Management
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="471" y="244" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    User...
                </text>
            </switch>
        </g>
        <rect x="541" y="221" width="70" height="40" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 68px; height: 1px; padding-top: 241px; margin-left: 542px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                Constrained
                                <br/>
                                Endpoint
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="576" y="245" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Constrained...
                </text>
            </switch>
        </g>
        <rect x="241" y="321" width="70" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 68px; height: 1px; padding-top: 351px; margin-left: 242px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                "Customer" VPCs
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="276" y="355" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    "Customer"...
                </text>
            </switch>
        </g>
        <rect x="251" y="331" width="70" height="60" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="none"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 68px; height: 1px; padding-top: 361px; margin-left: 252px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">
                                "Customer" VPCs
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="286" y="365" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    "Customer"...
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>
