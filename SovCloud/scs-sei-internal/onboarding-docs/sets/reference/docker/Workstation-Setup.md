# Workstation Setup

**Updated:** 04/21/2025

This guide will go over setting up your workstation for contributing to STE Build.

[[_TOC_]]

## Prerequisites
* An SAP I/C/D Number (and SAP account)
* An SAP Laptop
* Connection to SAP Corporate Network (Global Protect, license purchasing)
* CAM Profile Access (Done by Mentor)

## Obtain Docker License and Install Docker
* Check your software Licensing for existing license: [LINK](https://sapit-home-prod-004.launchpad.cfapps.eu10.hana.ondemand.com/site?sap-ushell-config=lean#mysoftware-Display?sap-ui-app-id-hint=fe5ca0d0-9694-4ad9-89bb-97471eaa03d7)
* Purchase Docker License (Requires Corporate Network Connection):
  * Start at Ariba: [LINK](https://s1-eu.ariba.com/gb/itemDetail/**********%2524%2524SSAM1/catalog?realm=SAPGLOBAL)
  * Click Link for "Software Catalog (SAP Network Required)'
  * Search for Docker Desktop and add to cart.
  * Checkout, (should return back to Ariba)
* Install Docker Desktop: [LINK](https://www.docker.com/products/docker-desktop)
* Once approved, you will receive an email instructing you to log into `Docker Desktop` with your SAP Email.

## Harbor Access (optional, see SAP Artifactory Access)
* Ask Sean or Louis to create an IAS account for you.
  * IAS Admin Page: https://ste.ias.preprod.scp.sapns2.us/admin  
* Login to Harbor: https://harbor.dev.ste.dev.scs.sap/
* Locally on CLI run -  `docker login harbor.dev.ste.dev.scs.sap`
* Use the CLI Token from the website Harbor

## SAP Public Artifactory Access

* Log in to [External Artifactory](https://scs-ste-general.common.repositories.cloud.sap) by clicking "SAML SSO" and entering your SAP email
* In the upper right, click on your name, click on "Edit Profile"
* Generate an Identity Token
* In CLI run: `docker login scs-ste-general.common.repositories.cloud.sap` 
  * Use your i# as the username and the CLI Token from the website as the password
* Pull the desired Docker image
  * STE Controller - bare minimum tools required to use STE Repository
  * Generic Workspace - STE Controller plus some quality of life tools, including vim-tiny, git, curl, etc.

## Use Docker Workspace
See guide [Using Docker Workspace](./Using-Docker-Workspace.md)
