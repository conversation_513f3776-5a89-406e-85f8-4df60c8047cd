# TFEnv Install

## Table of Contents
[TOC]

## Prerequisites
* Mac Laptop
* Mac Local Administrator
* [Homebrew](./homebrew.md)

## Summary:
Terraform itself is a single binary that can be simply downloaded and run.  However the terraform binaries are not necessarily backwards and forwards compatible.  TFEnv is a third party batch script that will scan the current folder to discover the version requirements of the code and will download and manage the versions of terraform binaries.

## Information
* Homepage: https://github.com/tfutils/tfenv

## Quick Instructions
1. Use homebrew to install `tfenv`
    * Alternate manual installation instructions can be found on the `Homepage`

## Detailed Instructions
<details><summary>Expand</summary><p>

  1. Run the following Code
      ```sh
      brew install tfenv
      ```
  </p></details>

## Appendix
### Quick Example of Commands
  * Reason:
    * Basic examples on how to use `tfenv`
  * <details><summary>Detailed Steps:</summary><p>

    * Detailed Steps Here
    ```sh
    tfenv install min-required    # Downloads the minimum version of terraform required by the code
    tfenv install latest          # Downloads the latest version of terraform available on the web
    tfenv install 1.6.1           # Downloads a specific version of terraform
    tfenv use min-required        # Switches to the minimum version of terraform required by the code
    tfenv use latest              # Switches to the latest version of terraform available on the web
    tfenv use 1.6.1               # Switches to a specific version of terraform.
    ```
    </p> </details>
