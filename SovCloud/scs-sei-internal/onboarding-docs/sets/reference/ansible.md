# Ansible Install

## Table of Contents
[TOC]

## Prerequisites
* Mac Laptop
* Mac Local Administrator
* [Python/Pip](./python_pip.md)

## Information
* Homepage: https://docs.ansible.com/ansible/latest/installation_guide/intro_installation.html

## Quick Instructions
1. Follow instructions on homepage for `pip` install
1. (Alternate) Follow instructions below.

## Detailed Instructions
* <details><summary>Expand</summary><p>

  1. Install [pip](./python_pip.md)
  1. Install Ansible (Full)
      ```sh
      python3 -m pip install --user ansible
      ```
  </p></details>

## Appendix
### Upgrading Ansible with Pip
* <details><summary>Expand</summary><p> 

  1. Upgrade command
      ```sh
      python3 -m pip install --upgrade --user ansible
      ```
  </p></details>

### Dependencies and libraries
These should already be installed if you chose the full ansible install.

The following is a list of frequently used and required packages that can be installed through pip. They can be installed with the command listed below:
```sh
python3 -m pip install --user ___PACKAGE1___,___PACKAGE2___,etc...
```

* <details><summary>Expand</summary><p> 

  ```
  boto
  boto3
  botocore
  Jinja2
  jq
  requests
  ```
  </p></details>

### Ansible Galaxy Collections
The following is a list of frequently used collections in addition to the `ansible full install` that can be installed through ansible-galaxy. They can be installed with the command listed below:
```sh
ansible-galaxy collection install ___PACKAGE1___,___PACKAGE2___,etc...
```

* <details><summary>Expand</summary><p> 

  ```
  amazon.aws
  community.aws
  google.cloud
  azure.azcollection
  ```
  </p></details>


### The Ansible Configuration File
* Ansible can be configured using this file: `~/.ansible.cfg`.
* The following is a small selection of possible options to set:
  * `roles_path` - This is used to locate the default location for available roles.
  * `enable_plugins` - Plugins allow you extend the behavior and functinality of ansible such as dynamic inventories.
  * Set `host_key_checking` - set this to false to disable ssh warnings for new machines that do not
  * Set `log_path`
  * Set `callbacks_enabled` - this will extend the look and feel of ansible outputs.
  * Configure SSH settings
      * See `ssh_args`

### Ansible Ad-Hoc Commands for validation
* `ansible -m ping 127.0.0.1`
* `ansible --version`
* `ansible-inventory -i localhost, --graph`
* `ansible-galaxy collection list`

### Additional Resources
* [Internal Ansible FAQ](https://gitlab.core.sapns2.us/stc-global/onboarding-and-training/general/-/blob/main/FAQs/ansible/faq-ansible.md)
