
# ___NOTE: THIS CONTENT IS UNDER CONSTRUCTION.___


# Ansible Loops

## Summary:
This contains documentation on how Loops work in Ansible

## Goals:
* Understand how Ansible Loops work

## Target Audience
* Ansible Engineers, (T1+)

## Terminology used

## Prerequisites

## Quiz
* N/A

## Link to Video
* Run this playbook locally with the command:
  ```sh
  ansible-playbook /tmp/___PLAYBOOK_NAME___.yml -i localhost, -c local
  ```

* <details><summary>Example playbook on Loops</summary><p>

  ```yaml
  ---
  - hosts: all
    gather_facts: false
    vars:
      myvar1: hello
      myvar2: world

    tasks:
    - name: basic debug example with message string
      ansible.builtin.debug:
        msg: "print string"

    - name: basic debug example with variable string
      ansible.builtin.debug:
        var: myvar1

    - name: basic debug example using task local variable
      vars:
        myvar1: greetings
      ansible.builtin.debug:
        var: myvar1

    - name: basic loop example using variables
      loop: ["myvar1","myvar2"]
      ansible.builtin.debug:
        var: "{{ item }}"

    - name: basic loop example using variables, different syntax
      loop:
        - myvar1
        - myvar2
      ansible.builtin.debug:
        var: "{{ item }}"

    - name: basic loop example using strings with variable substitution
      loop:
        - "{{ myvar1 }}"
        - "{{ myvar2 }}"
      ansible.builtin.debug:
        msg: "{{ item }}"

    - name: Loop example using a list of dictionaries.
      ansible.builtin.debug:
        msg: "Key1 is {{ item.key1 }}, Key2 is {{ item.key2 }}"
      loop:
        - key1: "{{ myvar1 }}"
          key2: "this"
        - key1: "{{ myvar2 }}"
          key2: "that"
        - key1: "bonjour"
          key2: "paddywhack"

    - name: Loop example using a list of dictionaries. Different Syntax
      ansible.builtin.debug:
        msg: "Key1 is {{ item.key1 }}, Key2 is {{ item.key2 }}"
      loop: [
        { key1: "{{ myvar1 }}", key2: "this" },
        { key1: "{{ myvar2 }}", key2: "that" },
        { key1: "bonjour", key2: "paddywhack" }
      ]

  ```
  </p></details>
