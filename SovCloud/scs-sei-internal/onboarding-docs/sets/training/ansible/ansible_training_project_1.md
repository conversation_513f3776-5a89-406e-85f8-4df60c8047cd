### Summary

Write some Ansible Automation to validate your understanding of the subject.

This automation will add a volume to a machine, install and configure some binaries, and configure logging for the binaries.

## Estimated Time
5 days

### Environment
* ___TO BE PROVIDED BY MENTOR/MANAGER___

### Instructions
1. Open a Branch in the Repository / Environment linked above.
2. Create a Merge Request from that Branch
3. Your goal is to create your own Ansible Code that fulfills the `Requested Enhancement` below.
4. Commit your changes to the Branch when you are done.
* **NOTE:** Do **NOT** modify or use an existing environment, environment state, or code.  You may use them as reference, but do not simply copy, clone, or branch existing code as your solution.

### Information 
* Available Shared Roles for use:
  * [Disk Management](https://gitlab.core.sapns2.us/scs/shared/ansible/roles/-/tree/main/disk-management?ref_type=heads)
  * [Repository Management](https://gitlab.core.sapns2.us/scs/shared/ansible/roles/-/tree/main/repository-management?ref_type=heads)


### Requested enhancement and output
Following the terraform training you should have at least 2 instances. One with only a private IP and one with a public IP.

Use Ansible to provision an instance to be a bastion with the following requirements:

**NOTE FOR MENTORS/MANAGER:** If the Public Instance has been configured as a VPN Server, use the Private Instance as the Bastion.  Otherwise use the Public Instance as the Bastion.


- [ ] Hostname is changed to "\<Employee ID\>-bastion"
  - [ ] The Employee ID should be passed as a variable
  - [ ] Ansible needs to enforce lowercase of hostname
- [ ] Add at least one additional volume using the "disk management" role
- [ ] Configure Red Hat repositories using the "repository management" role
- [ ] Download and Install the terraform binary on the bastion
- [ ] Install ansible on basion
- [ ] Configure Ansible through automation with the following:
  - [ ] Disable Ansible host-key checking (ansible.cfg)
  - [ ] Enable Ansible Logging to write to `/var/log/ansible`
    - [ ] Permissions for the log file should be set so that any user running Ansible will log their ansible execution
- [ ] Use Update `logrotate` behavior for `/var/log/ansible`
  - [ ] Rotate every 4 hours
  - [ ] Archive logs older than 24 hours
  - [ ] Append the prefix 'archive' to archived logs 
  - [ ] Suffix the archived log file with the date it was archived.
  - [ ] Move the archived logs to the newly attached volume

Bonus objectives ( in no particular order):
- [ ] logrotate multiple files
  - [ ] Use different rotation (time, size, etc) criteria
- [ ] Use tfenv to install terraform instead of using the binary


### Output Validation
- [ ] Single Ansible Playbook and optional Roles that provide the output above.
- [ ] Playbook should be idempotent and produce the same result regardless of initial state.
    * Playbook provides same result for a new machine.
    * Playbook provides same result for a partially configured machine.
    * Playbook provides same result for a completely configured machine.
- [ ] Playbook should be written with a generic of `all` target.
    * Target should be provided by inventory.
    * Target can be passed at command line by IP address or Inventory File.



### References and Relevant links
You may use the following ansible code as an example or as a base to build and extend on:

* [IBP Bastion Playbook](https://gitlab.core.sapns2.us/scs/s4pce/ansible/playbooks/-/merge_requests/14#ff86cc118b7a444ef22d2b111898adfc4360e641_129_98)

* [Ansible Bastion Role](https://gitlab.core.sapns2.us/i868402/Louis-Miscellaneous/-/tree/master/Ansible/roles/bastion/vars/business?ref_type=heads)

The following are related roles that will be required to complete this training.  Sample playbooks are also referenced to demonstrate how the roles may be used:

* [Disk Management](https://gitlab.core.sapns2.us/scs/shared/ansible/roles/-/tree/main/disk-management?ref_type=heads)
  * [Generic Disk Management Playbook](https://gitlab.core.sapns2.us/scs/shared/ansible/playbooks/-/blob/main/volume-create-resize.yml?ref_type=heads)
  * [IBP Customized Disk Management Playbook](https://gitlab.core.sapns2.us/scs/ibp/ansible/playbooks/-/blob/main/ibp-create-disks.yml?ref_type=heads)

* [Repository Management](https://gitlab.core.sapns2.us/scs/shared/ansible/roles/-/tree/main/repository-management?ref_type=heads)
  * [Generic Repository Management](https://gitlab.core.sapns2.us/scs/shared/ansible/playbooks/-/blob/main/repository-management.yml?ref_type=heads)

The following are examples of configuring logrotate with ansible:
* https://gitlab.core.sapns2.us/scs/shared/ansible/roles/-/blob/main/aws-automation-tools/tasks/cronbox-setup.yml?ref_type=heads
* https://gitlab.core.sapns2.us/scs/shared/ansible/roles/-/blob/main/base/tasks/log/redhat.yml?ref_type=heads
* https://gitlab.core.sapns2.us/scs/shared/ansible/roles/-/blob/main/haproxy-tcp-forward/tasks/main.yml?ref_type=heads#L66
* https://gitlab.core.sapns2.us/scs/shared/ansible
