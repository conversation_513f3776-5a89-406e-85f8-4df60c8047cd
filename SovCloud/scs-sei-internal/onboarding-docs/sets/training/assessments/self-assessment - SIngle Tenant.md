
# Learning Self Assessment - Single Tenant
The purpose of this self-assessment is to rank yourself in regards to critical skills identified by the team.  Please use an honest self assessment, as this information will be used to customize your onboarding experience and will serve as a benchmark for future training.

## Table of Contents
[TOC]

## Scoring System
* 0 - New to this Subject
* 1 - I have touched on this subject by following instructions/examples.
* 2 - I work with this subject regularly by following instructions/example.
* 3 - I modify or write instructions/examples for this subject.
* 4 - I understand the basics of this subject and understand how to search for more advanced features.
* 5 - I consider myself a power user of this subject.
* 6 - I am an expert user with knowledge of advanced features in this subject.
* 7 - I can create functions, features, solutions in this subject.
* 8 - I tutor others in this subject.
* 9 - I contribute to or am a developer of this subject.
* 10 - I am a recognized maintainer of the development team for this subject.

### Single Tenant Topics
| Topic               | Score | Comments                                           |
| :------------------ | :---: | :------------------------------------------------- |
| Debian / Ubuntu     |       | OS and tooling knowledge                           |
| Red Hat / CentOS    |       | OS and tooling knowledge                           |
| MacOS               |       | OS and tooling knowledge                           |
| Windows             |       | OS and tooling knowledge                           |
| ---                 | ---   | ---                                                |
| Bash Scripting      |       |                                                    |
| Python (Env)        |       | or similar languages ex. ruby/golang/rust/perl     |
| ---                 | ---   | ---                                                |
| Jira usage          |       | or similar ticketing systems                       |
| Git (General Usage) |       | or Github / Gitlab (General Usage)                 |
| Using VSCode        |       | or similar a Coding or Scripting IDE               |
| Creating Diagrams   |       |  TAM / UML / Flowchart Diagrams                    |
| ---                 | ---   | ---                                                |
| SAP Basis           |       | (installation, upgrades, migrations)               |
| SQL DB Management   |       | MSSQL, MySQL, etc.                                 |
| Non-Sql DB          |       | NoSQL, DynamoDB, Etc.                              |
| ---                 | ---   | ---                                                |
| Computer Networking |       | General Knowledge (CIDR, DNS, Packets, Etc)        |
| Cloud Infra         |       | Knowledge of of services, offerings and components |
| AWS Knowledge       |       | Amazon Web Services                                |
| Azure Knowledge     |       |                                                    |
| GCP Knowledge       |       | Google Cloud Platform                              |
| Openstack           |       | (Sovereign Cloud Infrastructure / Converged Cloud) |
| ---                 | ---   | ---                                                |
| Ansible             |       |                                                    |
| Terraform           |       |                                                    |
| Pipelines / CICD    |       | General Knowledge                                  |
| Gitlab CICD         |       |                                                    |
| Docker              |       |                                                    |
| Kubernetes          |       |                                                    |
