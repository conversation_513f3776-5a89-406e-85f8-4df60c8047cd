# Basic AWS
**NOTE:** This document need to be revised and split into smaller components.

## Summary:
The following links will guide you through basic AWS knowledge and usage.

## Goals:
* VPC (Virtual Private Cloud)
  * subnet
  * security groups
  * basic networking e.g., cidr blocks, ip address ranges
* EC2 (Elastic Compute Cloud)
  * tags
  * instance types
  * load balancer
  * images
  * SSH keys as pertains to AWS and Linux logins
    * key/pair
    * ssh keys
* S3 (Simple Storage Service)
  * create
* AWS CLI (advanced)
  * https://docs.aws.amazon.com/cli/index.html
  * regions
  * endpoints


## Target Audience
* Engineers, All, (T1+)

## Terminology used

## Prerequisites

## Quiz

## Links
Use the following link to access the documentation:

* Amazon AWS Documentation
  * [Documentation: AWS CLI](https://docs.aws.amazon.com/cli/latest/userguide/cli-chap-welcome.html)
  * [Documentation: AWS VPC](https://docs.aws.amazon.com/vpc/latest/userguide/what-is-amazon-vpc.html)
  * [Documentation: AWS EC2](https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/concepts.html)
* [Video: AWS Basics](https://www.youtube.com/watch?v=W6jQmVi31Xk&list=PLwyXYwu8kL0wg9R_VMeXy0JiK5_c70IrV)

### Linkedin Learning
* https://www.linkedin.com/learning/aws-essential-training-for-developers-17237791/what-is-the-best-way-to-use-aws
