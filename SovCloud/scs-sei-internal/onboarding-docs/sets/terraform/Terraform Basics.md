# Terraform Basics

## Summary:
The following videos will guide you through basic terraform knowledge and usage.

## Goals:
* Deploy example infrastructure on AWS
* Destroy example infrastructure
* Understand
  * File types (.tf, .tfvars, .tfstate)
  * Variables
  * Structures (providers, resources, variables, data sources)
  * Object types (string, list, map)
  * Interpolation
  * CLI (init, plan, apply)
  * Remote state ( backends )
    * State files


## Target Audience
* Engineers, All, (T1+)

## Terminology used

## Prerequisites
* terraform installed
* access to aws account

## Quiz

## Links
Use the following link to access the documentation:

* [Terraform Official Tutorial - AWS](https://developer.hashicorp.com/terraform/tutorials/aws-get-started)
* [Video: Terraform explained in 15 mins | Terraform Tutorial for Beginners](https://www.youtube.com/watch?v=l5k1ai_GBDE)
* [Document: Baeldung Intro to Terraform](https://www.baeldung.com/ops/terraform-intro)

## Linkedin Learning
* https://www.linkedin.com/learning/learning-terraform-********/learn-terraform-for-your-cloud-infrastructure
