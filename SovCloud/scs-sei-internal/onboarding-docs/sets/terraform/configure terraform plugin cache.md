# Configure Terraform Plugin Cache

## Summary:
By default terraform will download a new copy of plugins to the folder where the terraform code resides. We can save resources by setting a shared location for all plugins. This way, terraform will only download the plugins if that version does not exist.


## Goals:
* Configure terraform to use plugin cache

## Target Audience
* engineers, All, (T1+)

## Terminology used
* `.terraformrc` - File that stores configurations for terraform

## Prerequisites
* terraform installed and basic knowledge
* basic linux knowledge

## Quiz
* N/A

## Instructions
### Configure Terraform to use plugin cache
  * **Reason**:
    * Modify Terraform Configuration
  * **Steps**:
    1. Modify `~/.terraformrc`
    1. Set `plugin_cache_dir` to desired path
  * <details><summary>Detailed Steps:</summary><p>

    1. `code ~/.terraformrc`
    1. If it does not exists, add `plugin_cache_dir` and set the value to `"$HOME/.terraform.d/plugin-cache"`
        ```ini
        plugin_cache_dir   = "$HOME/.terraform.d/plugin-cache"
        ```
    </p> </details>
