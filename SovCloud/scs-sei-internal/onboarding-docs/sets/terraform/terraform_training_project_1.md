### Summary

Build an AWS environment using a layered terraform approach.

## Estimated Time
5 days

### Repository / Environment
* ___TO BE PROVIDED BY MENTOR/MANAGER___


### Instructions
1. Open a Branch in the Repository / Environment linked above.
2. Create a Merge Request from that Branch
3. Your goal is to create your own Terraform Code that fulfills the `Requested Enhancement` below.
4. Commit your changes to the Branch when you are done.
* **NOTE:** Do **NOT** modify or use an existing environment, environment state, or code.  You may use them as reference, but do not simply copy, clone, or branch existing code as your solution.

### Information 
* Available AMI for use:
  * Owner: `************`
  * Name: `Golden-SCS-Ubuntu-20.04-Base-V*`

### Requested enhancement and output

Use Terraform to deploy Infrastructure as Code that meet the following requirements:
- [ ] Layer-00
  - [ ] Deploy VPC
  - [ ] Deploy an IGW
  - [ ] Deploy at least one NGW
  - [ ] Deploy at least one public subnet
  - [ ] Deploy at least one private subnet
  - [ ] Use correct Routing
  - [ ] Use at least one ingress Security Group
  - [ ] Use at least one egress Security Group
- [ ] Layer-01
  - [ ] Declare at least one IAM profile
- [ ] Layer-02
  - [ ] Deploy one instance with only a private IP, assign the IAM Profile
  - [ ] Deploy one instance with a public IP, assign the IAM Profile
- [ ] Additional
  - [ ] Use at least one variable in each layer
  - [ ] Ensure all resources that support tags include a minimum set of tags:
    - `BuildUser` - your ID
    - `Description` - a short description of your choice
    - `Business` - "terraform training project 1"

Bonus objectives ( in no particular order):
- [ ] Use the [AWS-Instance](https://gitlab.core.sapns2.us/golden-ami-dev/ns2-terraform/-/tree/master/modules/aws-instance) module to create instances. Use this linked module, not one from the internet!
- [ ] Use loops to dynamically create Subnets
- [ ] Use loops to dynamically create Instances
- [ ] Deploy all IaC as a terraform module

### References and Relevant links

* [Example Infrastructure as Code](https://gitlab.core.sapns2.us/golden-ami-dev/ns2-terraform/-/tree/master/modules/build-project-aws-example)

* [Example Generic AWS Network Terraform Module](https://gitlab.core.sapns2.us/scs/shared/terraform/modules/-/tree/main/terraform-aws-network?ref_type=heads)

* [Example use of terraform layers and remote state calls](https://gitlab.core.sapns2.us/golden-ami-dev/ns2-terraform/-/tree/master/environments/ibp-development/ibp-testing)

* [Shared Modules](https://gitlab.core.sapns2.us/scs/shared/terraform/modules)

<!-- ACTION: Add estimated time e.g., 30min, 1d -->
### Stakeholder

@i868402

<!-- ACTION: Add appropriate labels -->
