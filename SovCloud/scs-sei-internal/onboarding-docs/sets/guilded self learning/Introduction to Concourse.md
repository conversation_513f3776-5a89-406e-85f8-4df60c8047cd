Goals: Create and execute a ConcourseCI job that executes

Completion Criteria: Demonstrate to mentor that the topics listed in this section are fully understood

Topics:
  * Understand purpose and functionality
  * Interacting with Fly CLI
  * Understanding Concourse teams
  * Create pipeline configuration file
    * Understand resources
    * Understand jobs
    * Understand tasks
    * Understand builds
  * Using variables files and pulling variables/secrets from Vault
  * Managing task containers
  * Creating and using custom docker images

Advanced Topics:
  * Using Jinja2 templating with the pipeline configuration file.
  * Using YAML anchors to create neater/easier to read pipeline configuration files.
  * Using the 'set_pipeline:' step with a git resource to create a self-updating pipeline.
  * Increasing pipeline efficiency by running jobs and tasks in parallel.
    * Using 'serial_groups' parameter for jobs.
    * Using 'in_parallel' step for tasks.

Learning Materials:
  * Concourse Quick Start Guide
    * Great place for just starting out, all you need is Docker on your local machine
    * After completing the Quick Start guide review https://concourse-ci.org/how-to-guides.html 1.14.1 to ******** attempt to enhance the Quick Start project with some of the common practices (where it makes sense). Try adding a file to a feature branch and checking in the file and pushing the branch.
  * https://concoursetutorial.com/
    * Good overview of basics. Go through basic tutorial section.
  * https://www.digitalocean.com/community/tutorials/how-to-set-up-continuous-integration-pipelines-with-concourse-ci-on-ubuntu-16-04
    * Another overview of basics.
  * https://concourse-ci.org/index.html
    * Official documentation for Concourse
  * https://gitlab.core.sapns2.us/golden-ami-dev/golden-ami-pipeline/-/blob/master/create-pipeline/concourse-pipeline/example-pipeline/TRAINING.md
    * NS2 training on how to upload a Concourse pipeline, interact with it, and tear it down to learn the basic fundamentals of Concourse.
  * https://gitlab.core.sapns2.us/golden-ami-dev/golden-ami-pipeline/-/blob/master/create-pipeline/concourse-pipeline/example-pipeline/example-pipeline-basic/pipeline-configuration.yml
    * Example Concourse pipeline that demonstrates and explains very basic Concourse features.
  * https://gitlab.core.sapns2.us/golden-ami-dev/golden-ami-pipeline/-/tree/master/create-pipeline/concourse-pipeline/example-pipeline/example-pipeline-intermediate
    * Example Concourse pipeline that demonstrates more advanced Concourse features, such as Vault integration and a variables file.
