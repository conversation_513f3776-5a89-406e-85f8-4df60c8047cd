Content Last refreshed - 2021-10-20

Purpose: Familiarize new users with basic knowledge of the YAML syntax and format.

Prerequisites: None

Goals: Understand the listed topics

Completion Criteria: Demonstrate to mentor that the topics listed in this section are fully understood

Topics:
  * Basic Structure
    * Document Start ---
    * Document End …
  * Dictionaries
  * Lists
  * Literal vs Folded Block Scalar
    * |
    * >
  * Variable Substitution

Learning Materials:
  * Ansible reference for YAML
  https://docs.ansible.com/ansible/latest/reference_appendices/YAMLSyntax.html
  * A Cloud Guru video series on YAML (2 hour completion)
  https://learn.acloud.guru/course/90dd551f-91a6-4b91-a0a8-d4905521f641/dashboard
