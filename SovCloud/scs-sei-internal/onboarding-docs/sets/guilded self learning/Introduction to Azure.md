Content Last refreshed - 2021-11-03

Purpose: The purpose of this exercise is to familiarize new users with basic Azure concepts

Goals: Understand and create resources listed in the topics

Completion Criteria: Demonstrate to mentor that the topics listed in this section are fully understood

Topics:
  * Vnet (Virtual Network)
    * subnet
    * security groups
    * basic networking e.g., cidr blocks, ip address ranges
  * VM (Virtual Machines)
    * tags
    * instance types
    * load balancer
    * images
    * SSH keys as pertains to Azure and Linux logins
      * key/pair
      * ssh keys
  * Storage Accounts
  * Resource Groups
  * Azure Authentication with Service Principles (advanced)
    * https://docs.microsoft.com/en-us/powershell/azure/authenticate-azureps
    * https://docs.microsoft.com/en-us/cli/azure/authenticate-azure-cli
  * Azure CLI (advanced)
    * https://learn.acloud.guru/course/6bc393e8-5fd6-440a-8b13-e76375a2021f/overview
    * https://app.pluralsight.com/library/courses/azure-cli-getting-started/table-of-contents
    * https://docs.microsoft.com/en-us/cli/azure/?view=azure-cli-latest
  * Azure PowerShell (advanced)
    * https://docs.microsoft.com/en-us/powershell/azure/?view=azps-2.8.0

Learning Materials:
  * A Cloud Guru - Azure Concepts
  https://learn.acloud.guru/course/introduction-to-azure/overview
  * A Cloud Guru - Into to creating Azure Blob Storage
  https://learn.acloud.guru/handson/60e3ea2f-864c-4c81-9132-da9b0cf88d67
  * Microsoft Azure use cases
    * Azure CLI Essentials : https://learn.acloud.guru/course/6bc393e8-5fd6-440a-8b13-e76375a2021f/overview
  * Unvalidated Resources
    * Terraform infrastructure (prior Terraform knowledge required)
      * https://learn.acloud.guru/course/bd8060c6-e408-4801-a4a3-8317c45319bf/overview
