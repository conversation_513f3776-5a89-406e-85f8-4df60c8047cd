Purpose: Learn what Gardener is, it's overall architecture, how to manage it, and how to use it deploy kubernetes workloads.

Prerequisites: Containers, Kubernetes, AWS/GCP, Git, Yaml, Ansible, Python, Networking, Concourse, DNS,

Goals: Show understanding of the overall Gardener Kubernetes Platform and key topics.

Completion Criteria: Demonstrate to mentor that the topics listed in this section are fully understood.

Topics:
  * Garden cluster
  * Seed clusters
  * Gardener Projects
  * Gardener Project and Shoot cluster kubeconfigs
  * Gardener Infrastructure Secrets
  * Shoot clusters
  * Worker Groups and Scaling
  * Gardener DNS objects
  * Gardener SSL Certificate objects
  * Gardener Machine Images
    * Ubuntu-Pro (i.e. NS2 Hardened Ubuntu 18.04 images)
  * Gardener Deployment Pipeline

Learning Materials:
  * Getting Started using Gardener to deploy Kubernetes Workloads:
    * https://pages.github.tools.sap/kubernetes/gardener/docs/012-getting-started/
  * Understanding Gardener Internals:
    * https://github.com/gardener/gardener/blob/master/docs/concepts/architecture.md#detailed-architecture-diagram
    * https://github.com/gardener/gardener/tree/master/docs
    * https://github.com/gardener/gardener
    * https://gardener.cloud/docs/
  * Gardener Exercise:
    * https://github.com/SAP-samples/teched2020-DEV269
  * Gardener Dashboard:
    * https://dashboard.garden.ns2-cn.gardener.cloud/namespace/garden/shoots/
  * NS2 Canary Gardener Landscape:
    * https://github.wdf.sap.corp/kubernetes-ns2-canary/landscape-ns2-canary-garden
  * Ns2 Canary Gardener Pipeline:
    * https://concourse.ci.gardener.cloud.sap/teams/ns2/pipelines/landscape-ns2-canary-jobs-master
  * Technologies useful in managing the Gardener Platform
    * https://learn.acloud.guru/custom-learning-path/1f407b70-6d8e-47f4-939a-0435788f216c
