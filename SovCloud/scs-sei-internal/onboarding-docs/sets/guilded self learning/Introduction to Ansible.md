Content Last refreshed - 2021-03-11

Purpose: Familiarize new users with basic ansible knowledge and useability from the command line

Prerequisites: Introduction to YAML

Goals: Show ability to implement core topics. Show understanding of additional concepts

Completion Criteria: Demonstrate to mentor that the topics listed in this section are fully understood

Topics:
  * Execution
    * Remote and Local
    * privilege escalation
  * Playbooks, Plays, Roles, Tasks
    * Learn the functions and difference of each topic
    * Become familiar with utilizing each concept
  * Modules
    * Understand the concept of a module
    * Running commands from a shell
    * Conditionals statements and logic flow
  * File Structure
    * Contents of a role
  * Variables (order of precedence)
    * facts vs variables vs extra-vars
  * Additional concepts to understand (not necessarily implement)
    * Host / Group Inventory Vars
    * Looping
    * Fact gathering
    * Jinja filters
    * Review Best Practices

Learning Materials:
  * Ben's IT Tutorials (YouTube)
  https://www.youtube.com/playlist?list=PLFiccIuLB0OiWh7cbryhCaGPoqjQ62NpU
    * Watch all 5 videos which should cover majority of the topics
    * This video is a bit dated so there are some deprecation warnings.
    * Source code can be found here
      * For the app server install pip then use pip to install Python packages instead of apt-get
    * Consider using an NGINX as the proxy instead of Apache 2
    * This can be done locally with docker-compose and the base Ubuntu image.
      * To retrieve the up of containers use "getent hosts app_server | awk '{ print $1 }'" if it's not in hostvars
  * Jeff Geerling Ansible series (YouTube optional)
  Ansible 101 - https://www.youtube.com/watch?v=goclfp6a2IQ&list=PL2_OBreMn7FqZkvMYt6ATmgC0KAGGJNAN
  Ansible - https://www.youtube.com/watch?v=93urFkaJQ44&list=PL2_OBreMn7FplshFCWYlaN2uS8et9RjNG
  * A Cloud Guru Introduction to Ansible
  * A Cloud Guru Hands On Learn Ansible by Doing
  * A Cloud Guru Learning Ansible 2.7 (Full Course)
  * Ansible Official Site
  https://docs.ansible.com/ansible/latest/index.html
    * (optional) for official documentation
