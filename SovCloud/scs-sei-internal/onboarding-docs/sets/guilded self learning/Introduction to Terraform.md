Content Last refreshed - 2021-10-20

Purpose: Familiarize new users with basic terraform concepts

Goals: Stand up project VPC with all of the relevant components

Completion Criteria: Demonstrate to mentor that the topics listed in this section are fully understood

Dependencies:
  * Basic Git
  * AWS
  * Terraform 1.0+

Topics:
  * File types (.tf, .tfvars, .tfstate)
  * Structures (providers, resources, variables, data sources)
  * Object types (string, list, map)
  * Interpolation
  * CLI (init, plan, apply)
  * Remote state ( backends )
    * State files
  * Workflow


Learning Materials (2023-06-07):
  * [Terraform Official Tutorial - AWS](https://developer.hashicorp.com/terraform/tutorials/aws-get-started)
    * [Youtube](https://www.youtube.com/watch?v=l5k1ai_GBDE)
  * [Baeldung Intro to Terraform](https://www.baeldung.com/ops/terraform-intro)
  * [Layering with Terraform](https://medium.com/@david.alvares.62/the-layering-method-with-terraform-d06e1e851a34#:~:text=Layering%20is%20a%20technique%20for,the%20form%20of%20separate%20directories.)
    * [Youtube](https://www.youtube.com/watch?v=0UHkKZjM_iM)
  * [SovCloud Generic AWS Module](https://gitlab.core.sapns2.us/scs/shared/terraform/modules/-/tree/main/terraform-aws-network?ref_type=heads)

OLD Learning Materials:
  * Terraform official tutorial
A Cloud Guru - AWS Terraform/Ansible Course
    * When using aCloud environments you may want to write some automation because each lesson of the 9 hour course builds on the previous lesson and assumes that the environment is setup accordingly but aCloud environments self destruct in about 2-3 hours.
      * Automate the terraform remote s3 backend bucket creation with terraform and bash
      * Automate creating the terraform user and ec2 policies/roles with terraform
    * Amazon Linux images have deployed Hotpatch for Apache Log4j this interferes with Jenkins startup see this issue
      * As a work around you can disable the patch service
    * Jenkins logs on Linux are accessed by journalctl so you'll have to modify the anslible playbooks.
      * If you still want to grep the logs search for "Started Jenkins Continuous Integration Server." instead of what aCloud says.
      * "if journalctl -u jenkins.service | grep "Started Jenkins Continuous Integration Server"; then…"
    * aws_acm_certificate_validation can take up to 30 minutes as per AWS docs
    * Make sure you have permission to build infrastructure before starting
    * Use the development workstation to follow along.
    * Terraform is installed on all workstations.
    * You do not need to provide AWS access/secret key. This is handled by the machine roles of the workstation.
    * Your region should be set to us-gov-west-1
  * Layering with Terraform state explained (Highly recommended before attempting the training exercise)
  * Using Terraform to Manage Applications and Infrastructure (Chapter 3)
    * Explains structure (providers, resources, variables, etc…)
  * NS2 Build example AWS module
  * A Cloud Guru - Provision an AWS Resource with Terraform
