See GitLab NS2-Training: https://gitlab.core.sapns2.us/golden-ami-dev/ns2-training

Git Usage Exercises 01
https://gitlab.core.sapns2.us/golden-ami-dev/ns2-training/-/tree/master/exercises/01

The purpose of this exercise is to familiarize new users with basic git usage.

Goals
  * Repository cloning
  * Branch creation and usage
  * Committing changes
  * Review changes
  * Merge request process

Open GitLab for easy reference
  * Open Web Browser and navigate to https://gitlab.core.sapns2.us/golden-ami-dev/ns2-training

Using GitHub Desktop
  * Create local directory for storage of cloned repositories e.g., `/home/<USER>/repositories/` or `C:\Users\<USER>\repositories\` if you don't want to use default location
  * Open GitHub Desktop and select `File` then select `Clone Repository`
  * Paste in repository URL from GitLab `https://gitlab.core.sapns2.us/golden-ami-dev/ns2-training`
  * Add a local path for your local repository folder
  * Select clone
  * Enter username and password (without multifactor authentication (MFA)) or use deploy tokens if you have MFA
  * Open this file locally to demonstrate successful cloning

Create a branch
  * Open GitHub Desktop select `Branch` and select `New Branch`
  * Enter in your user ID plus a brief description e.g., `c123456 - performing training exercise` as the new branch name
  * Select ok
  * Select publish
  * Ensure you are on your own branch rather than master before continuing

Switching branches and repositories
  * Open GitHub Desktop
  * The top left button ('Current Repositories') allows you to switch between different repositories
  * The top middle button ('Current branch') allows you to switch between different branches
  * The top right button allows you to synchronize with the current repo and branch (fetch/pull/push)
    * Fetch: Get the latest state of the repository and branch on the remote (server)
    * Pull: Saves the latest changes on the remote to your local copy
    * Push: Saves the latest changes on your local copy to the remote.

Commit a change
  * Open GithubDesktop and change current branch to the newly created branch
  * Open text editor and save a file called `hello-username` where username is your user id within the `git usage` directory
  * Open GitHubDesktop - Make sure the change is checked
  * Add commit name and description then select commit
  * Select `Push to Origin`

Comparing Branches
  * Open GitHubDesktop and select `Branch` then select `Compare to Branch`
  * Select `Master`
  * Look at the behind/ahead

Create Merge Requests (Pull Requests)
  * Open Web Browser and navigate to GitLab `https://gitlab.core.sapns2.us/golden-ami-dev/ns2-training`
  * Select `Merge Requests` from the left hand navigation
  * Select `New Merge Request`, select your newly created branch, and then select `Compare Branches and continue`
  * Update merge request with new title
  * Tag merge request with appropriate label e.g., `documentation`
  * Update merge request content with the following content:
```
This merge request addresses the following items:
* adding example file to master for training purposes
The justification for this merge request is as follows:
* demonstrate git proficiency
```
  * Select `Remove source branch when merge request is accepted.` to ensure branch deletion after merging
  * Assign merge request to peer for review
  * Make modifications from peer feedback
  * Ensure peer is okay with changes before assigning to approver for merging into master

Advanced Topics
  * Branch deletion
  * Merge conflicts
  * Stale branches
  * Git blame
  * Rebasing
