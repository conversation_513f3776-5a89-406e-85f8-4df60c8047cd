Purpose: tbd

Prerequisites: tbd

Goals: Show ability to implement core topics. Show understanding of additional concepts

Completion Criteria: Demonstrate to mentor that the topics listed in this section are fully understood

Topics:
  * Helm Commands
    * Template
    * Install
    * Upgrade
    * Rollback
  * Folder Structure
    * Chart.yaml
    * charts/
    * Templates/
      ▪ Deployment.yaml
      ▪ Ingress.yaml
      ▪ Service.yaml
      ▪ Serviceaccount.yaml
      ▪ Etc ….
    * values.yaml
  * Charts
    * Values file(s)
  * Chart Hooks
  * Go Templating


Learning Materials:
  * YouTube - Intro to Helm
  https://www.youtube.com/watch?v=5_J7RWLLVeQ
  * A Cloud Guru - Helm Deep Dive v3
https://learn.acloud.guru/course/helm-deep-dive-v3/dashboard
  * A Cloud Guru - Deploying to Kubernetes Using Helm
https://learn.acloud.guru/course/helm-deep-dive-v3/learn/53ab7fbd-eb46-4dc6-95ab-a2e8b0980e66/eee88026-5c8c-4b7d-b915-283323ebfdc5/watch
  * Helm Documentation
https://helm.sh/docs/
