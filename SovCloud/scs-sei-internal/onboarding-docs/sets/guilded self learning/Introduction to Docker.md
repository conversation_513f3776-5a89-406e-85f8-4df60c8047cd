Purpose: Familiarize new users with basic docker knowledge

Goals: Understand docker as a end-user and also create a container and configure a service within it

Completion Criteria: Demonstrate to mentor that the topics listed in this section are fully understood

Topics:
  * Understand containers
  * Interacting with containers
    * starting/stopping/rebuilding/destroying
  * Understand Docker file format
  * Being able to exec into a container
    * docker run/exec/build
  * Understand Docker registries (AWS ECR)

Learning Materials:
  * Beginner's Guide to Containers/Orchestration
https://learn.acloud.guru/course/f276f2aa-fb5e-4fc6-9c36-0dc674b95767/dashboard
    * Watch all videos
  * Docker Quickstart
  https://learn.acloud.guru/course/da6947b1-0901-4f60-a045-c15ec895a3d9/overview
    * Dockerfile
    * Image creation and management
    * Container ports
    * Container Volumes
    * Hands on lab
  * Creating your own Docker image
  https://learn.acloud.guru/handson/dec6b6ca-4a95-4e08-a815-945c9ce47021
    * Build NGINX container
  * Docker Essentials
https://www.udemy.com/docker-essentials/?ranMID=39197&ranEAID=JVFxdTr9V80
    * Docker Network (Multi-container Applications, Introduction to Container Networking Model and Docker Network Drivers, Remainder Optional)
    * Docker Storage (Optional)
    * Container Orchestration with Docker Storm (Skip)
