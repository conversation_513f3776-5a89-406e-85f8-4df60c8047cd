# Usage:
# Rename `.env-template` to `.env` and update the values
# `docker compose up -d` or `docker compose run ___service_name___`
# `docker compose config --services` or `docker compose ps`
# `docker compose attach ___service_name___`
# In Container, `ctl+pq` to detach from the container.
# In Container, `cat /etc/motd` to see the welcome message.
# NOTE:
#   - Adding "bridged" network to services can result in dns to not resolve properly.
#   - Look into a DNS Container. There's a prepackaged Alpinelinux/unbound. Also see: https://pmig.at/en/tech/guides/docker-dns-server/

services:
  ste_controller:
    tty: true
    restart: always
    stdin_open: true
    image: ${ARTIFACT_SERVER}/ste_controller:${STE_CONTROLLER_VERSION}
    volumes:
      - ${STE_AUTOMATION_REPO}:/mnt/ste_automation
      - ${ADDITIONAL_REPOS_PATH}:/mnt/repos
      - ${AWS_CONFIG_PATH}:/root/.aws/config
      - ${AWS_CREDENTIAL_PATH}:/root/.aws/credentials
      - ${OS_CONFIG}:/root/.config
      - ${SSH_KEY_PATH}:/root/.ssh
    environment:
      - TF_VAR_build_user=${TF_VAR_build_user}
      - AWS_PROFILE=${AWS_PROFILE}
      - OS_CLOUD=${OS_CLOUD} # Openstack Cloud Environment
    #dns: ${DNS_SERVER}     # For Current implementation of SCI this must be disabled for now.
    #dns_search: ${DNS_SEARCH} # For Current implementation of SCI this must be disabled for now.
    # DNS is an issue with OpenStack
    # platform: linux/amd64
    # platform: linux/arm64/v8

  generic_workspace:
    tty: true
    restart: always
    stdin_open: true
    image: ${ARTIFACT_SERVER}/generic_workspace:${GENERIC_WORKSPACE_VERSION}
    volumes:
      - ${STE_AUTOMATION_REPO}:/mnt/ste_automation
      - ${ADDITIONAL_REPOS_PATH}:/mnt/repos
      - ${AWS_CONFIG_PATH}:/root/.aws/config
      - ${AWS_CREDENTIAL_PATH}:/root/.aws/credentials
      - ${SSH_KEY_PATH}:/root/.ssh
    environment:
      - TF_VAR_build_user=${TF_VAR_build_user}
      - AWS_PROFILE=${AWS_PROFILE}
    #dns: ${DNS_SERVER}
    #dns_search: ${DNS_SEARCH}
    # platform: linux/amd64
    # platform: linux/arm64/v8
